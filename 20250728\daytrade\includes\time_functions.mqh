//+------------------------------------------------------------------+
//|                                              time_functions.mqh |
//|                                  Copyright 2025, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, MetaQuotes Ltd."
#property link      "https://www.mql5.com"

#include "constants.mqh"
#include "globals.mqh"
#include "error_handler.mqh"

//+------------------------------------------------------------------+
//| 初始化时间模块                                                   |
//+------------------------------------------------------------------+
bool InitializeTimeModule()
{
    // 时间模块初始化逻辑
    return true;
}

//+------------------------------------------------------------------+
//| 清理时间模块                                                     |
//+------------------------------------------------------------------+
void CleanupTimeModule()
{
    // 清理时间模块资源
}

//+------------------------------------------------------------------+
//| 获取当前时间戳（避免重复定义）                                   |
//+------------------------------------------------------------------+
#ifndef GET_CURRENT_TIMESTAMP_DEFINED
#define GET_CURRENT_TIMESTAMP_DEFINED
datetime GetCurrentTimestamp()
{
    return TimeCurrent();
}
#endif

//+------------------------------------------------------------------+
//| 获取今日开始时间                                                 |
//+------------------------------------------------------------------+
datetime GetTodayStart()
{
    datetime current_time = TimeCurrent();
    return (datetime)(current_time / 86400) * 86400;
}

//+------------------------------------------------------------------+
//| 判断是否为新的一天                                               |
//+------------------------------------------------------------------+
bool IsNewDay()
{
    static datetime s_last_day = 0;
    datetime today_start = GetTodayStart();
    
    if(s_last_day != today_start)
    {
        s_last_day = today_start;
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| 判断是否需要更新时段蓝图                                         |
//+------------------------------------------------------------------+
bool IsNewDayForBlueprint()
{
    datetime current_time = TimeCurrent();
    datetime last_update = g_context.last_blueprint_update;
    
    // 如果从未更新过，或者距离上次更新超过20小时
    if(last_update == 0 || (current_time - last_update) > 72000) // 20小时 = 72000秒
    {
        return true;
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| 判断是否需要更新关键价位                                         |
//+------------------------------------------------------------------+
bool IsNewDayForKeyLevels()
{
    datetime current_time = TimeCurrent();
    datetime last_update = g_context.key_levels_update;
    
    // 如果从未更新过，或者距离上次更新超过18小时
    if(last_update == 0 || (current_time - last_update) > 64800) // 18小时 = 64800秒
    {
        return true;
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| 判断是否需要更新EMA统计                                          |
//+------------------------------------------------------------------+
bool IsTimeToUpdateEMAStats()
{
    datetime current_time = TimeCurrent();
    datetime last_update = g_context.ema_stats_update;
    
    // 每5分钟更新一次EMA统计
    if(last_update == 0 || (current_time - last_update) > 300) // 5分钟 = 300秒
    {
        return true;
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| 伦敦夏令时判断（优化缓存版本）                                   |
//+------------------------------------------------------------------+
bool IsLondonInDST(datetime check_time)
{
    PERFORMANCE_MONITOR();
    
    MqlDateTime dt;
    TimeToStruct(check_time, dt);
    
    int year = dt.year;
    int month = dt.mon;
    int day = dt.day;
    int hour = dt.hour;
    
    // 伦敦夏令时规则：3月最后一个周日 01:00 GMT 开始，10月最后一个周日 01:00 GMT 结束
    
    // 计算3月最后一个周日
    datetime march_last_sunday = GetLastSundayOfMonth(year, 3);
    MqlDateTime march_dt;
    TimeToStruct(march_last_sunday, march_dt);
    march_dt.hour = 1; // 01:00 GMT
    datetime dst_start = StructToTime(march_dt);
    
    // 计算10月最后一个周日
    datetime october_last_sunday = GetLastSundayOfMonth(year, 10);
    MqlDateTime october_dt;
    TimeToStruct(october_last_sunday, october_dt);
    october_dt.hour = 1; // 01:00 GMT
    datetime dst_end = StructToTime(october_dt);
    
    bool in_dst = (check_time >= dst_start && check_time < dst_end);
    
    LogDebug(StringFormat("伦敦夏令时检查: %s, 结果: %s", 
                         TimeToString(check_time, TIME_DATE|TIME_MINUTES), 
                         in_dst ? "夏令时" : "标准时"), __FUNCTION__);
    
    return in_dst;
}

//+------------------------------------------------------------------+
//| 纽约夏令时判断（优化缓存版本）                                   |
//+------------------------------------------------------------------+
bool IsNewYorkInDST(datetime check_time)
{
    PERFORMANCE_MONITOR();
    
    MqlDateTime dt;
    TimeToStruct(check_time, dt);
    
    int year = dt.year;
    
    // 纽约夏令时规则：3月第二个周日 02:00 EST 开始，11月第一个周日 02:00 EST 结束
    
    // 计算3月第二个周日
    datetime march_second_sunday = GetSecondSundayOfMonth(year, 3);
    MqlDateTime march_dt;
    TimeToStruct(march_second_sunday, march_dt);
    march_dt.hour = 7; // 02:00 EST = 07:00 GMT (标准时)
    datetime dst_start = StructToTime(march_dt);
    
    // 计算11月第一个周日
    datetime november_first_sunday = GetFirstSundayOfMonth(year, 11);
    MqlDateTime november_dt;
    TimeToStruct(november_first_sunday, november_dt);
    november_dt.hour = 6; // 02:00 EST = 06:00 GMT (夏令时)
    datetime dst_end = StructToTime(november_dt);
    
    bool in_dst = (check_time >= dst_start && check_time < dst_end);
    
    LogDebug(StringFormat("纽约夏令时检查: %s, 结果: %s", 
                         TimeToString(check_time, TIME_DATE|TIME_MINUTES), 
                         in_dst ? "夏令时" : "标准时"), __FUNCTION__);
    
    return in_dst;
}

//+------------------------------------------------------------------+
//| 获取缓存的伦敦夏令时状态                                         |
//+------------------------------------------------------------------+
bool GetCachedLondonDST(datetime check_time)
{
    datetime today_start = GetTodayStart();
    
    // 检查缓存是否有效
    if(g_context.dst_cache.is_valid && g_context.dst_cache.cache_date == today_start)
    {
        return g_context.dst_cache.london_dst;
    }
    
    // 缓存无效，重新计算
    bool london_dst = IsLondonInDST(check_time);
    
    // 更新缓存
    g_context.dst_cache.london_dst = london_dst;
    g_context.dst_cache.cache_date = today_start;
    g_context.dst_cache.is_valid = true;
    
    LogInfo(StringFormat("更新伦敦夏令时缓存: %s", london_dst ? "夏令时" : "标准时"), __FUNCTION__);
    
    return london_dst;
}

//+------------------------------------------------------------------+
//| 获取缓存的纽约夏令时状态                                         |
//+------------------------------------------------------------------+
bool GetCachedNewYorkDST(datetime check_time)
{
    datetime today_start = GetTodayStart();
    
    // 检查缓存是否有效
    if(g_context.dst_cache.is_valid && g_context.dst_cache.cache_date == today_start)
    {
        return g_context.dst_cache.newyork_dst;
    }
    
    // 缓存无效，重新计算
    bool newyork_dst = IsNewYorkInDST(check_time);
    
    // 更新缓存
    g_context.dst_cache.newyork_dst = newyork_dst;
    g_context.dst_cache.cache_date = today_start;
    g_context.dst_cache.is_valid = true;
    
    LogInfo(StringFormat("更新纽约夏令时缓存: %s", newyork_dst ? "夏令时" : "标准时"), __FUNCTION__);
    
    return newyork_dst;
}

//+------------------------------------------------------------------+
//| 获取指定月份的最后一个周日                                       |
//+------------------------------------------------------------------+
datetime GetLastSundayOfMonth(int year, int month)
{
    // 获取下个月的第一天
    int next_month = month + 1;
    int next_year = year;
    if(next_month > 12)
    {
        next_month = 1;
        next_year++;
    }
    
    MqlDateTime dt;
    dt.year = next_year;
    dt.mon = next_month;
    dt.day = 1;
    dt.hour = 0;
    dt.min = 0;
    dt.sec = 0;
    dt.day_of_week = 0;
    dt.day_of_year = 0;
    
    datetime next_month_start = StructToTime(dt);
    datetime last_day_of_month = next_month_start - 86400; // 减去一天
    
    // 找到最后一个周日
    TimeToStruct(last_day_of_month, dt);
    int days_to_subtract = dt.day_of_week; // 0=周日, 1=周一, ..., 6=周六
    
    return last_day_of_month - (days_to_subtract * 86400);
}

//+------------------------------------------------------------------+
//| 获取指定月份的第二个周日                                         |
//+------------------------------------------------------------------+
datetime GetSecondSundayOfMonth(int year, int month)
{
    // 获取第一个周日
    datetime first_sunday = GetFirstSundayOfMonth(year, month);
    
    // 加上7天得到第二个周日
    return first_sunday + (7 * 86400);
}

//+------------------------------------------------------------------+
//| 获取指定月份的第一个周日                                         |
//+------------------------------------------------------------------+
datetime GetFirstSundayOfMonth(int year, int month)
{
    MqlDateTime dt;
    dt.year = year;
    dt.mon = month;
    dt.day = 1;
    dt.hour = 0;
    dt.min = 0;
    dt.sec = 0;
    dt.day_of_week = 0;
    dt.day_of_year = 0;
    
    datetime first_day = StructToTime(dt);
    TimeToStruct(first_day, dt);
    
    // 计算到第一个周日需要加的天数
    int days_to_add = (7 - dt.day_of_week) % 7; // 0=周日, 1=周一, ..., 6=周六
    
    return first_day + (days_to_add * 86400);
}

//+------------------------------------------------------------------+
//| 获取当前交易时段                                                 |
//+------------------------------------------------------------------+
ENUM_TRADING_SESSION GetCurrentTradingSession()
{
    PERFORMANCE_MONITOR();
    
    datetime current_gmt = TimeGMT();
    
    // 获取当天的夏令时状态（使用缓存）
    bool london_dst = GetCachedLondonDST(current_gmt);
    bool newyork_dst = GetCachedNewYorkDST(current_gmt);
    
    // 获取当天0点GMT时间作为基准
    datetime today_start = GetTodayStart();
    
    // 计算关键时间点（GMT小时）
    int london_open_hour = london_dst ? 7 : 8;                    // 伦敦开盘
    int london_close_hour = london_dst ? 16 : 17;                 // 伦敦收盘
    int newyork_open_hour = newyork_dst ? 13 : 14;                // 纽约开盘（简化为整点）
    int newyork_close_hour = 22;                                  // 纽约收盘
    int asia_start_hour = 22;                                     // 亚洲开始（前一天）
    
    // 计算具体时间点
    datetime london_open = today_start + london_open_hour * 3600;
    datetime london_close = today_start + london_close_hour * 3600;
    datetime newyork_open = today_start + newyork_open_hour * 3600 + 30 * 60; // 加30分钟偏移
    datetime newyork_close = today_start + newyork_close_hour * 3600;
    datetime asia_start = today_start - 2 * 3600; // 前一天22:00
    
    // 时段判断逻辑（v12版本 - 纽约时段拆分）
    if(current_gmt >= asia_start && current_gmt < london_open)
    {
        return SESSION_ASIA;
    }
    else if(current_gmt >= london_open && current_gmt < newyork_open)
    {
        return SESSION_LONDON;
    }
    else if(current_gmt >= newyork_open && current_gmt < london_close)
    {
        return SESSION_OVERLAP; // 重叠时段（多方角力）
    }
    else if(current_gmt >= london_close && current_gmt < newyork_close)
    {
        return SESSION_NEWYORK; // 纽约后半段（一方主导）
    }
    else
    {
        return SESSION_NONE; // 非主要时段
    }
}

//+------------------------------------------------------------------+
//| 获取时段名称                                                     |
//+------------------------------------------------------------------+
string GetSessionName(ENUM_TRADING_SESSION session)
{
    switch(session)
    {
        case SESSION_ASIA:    return "亚洲时段";
        case SESSION_LONDON:  return "伦敦时段";
        case SESSION_OVERLAP: return "重叠时段(多方角力)";
        case SESSION_NEWYORK: return "纽约后半段(一方主导)";
        case SESSION_NONE:    return "非主要时段";
        default:              return "未知时段";
    }
}

// GetSessionColor函数已在constants.mqh中定义，此处移除重复定义

//+------------------------------------------------------------------+
//| 计算每日时段蓝图                                                 |
//+------------------------------------------------------------------+
void CalculateDailySessionBlueprint()
{
    PERFORMANCE_MONITOR();
    
    LogInfo("开始计算每日时段蓝图（v12版本 - 纽约时段拆分）", __FUNCTION__);
    
    datetime current_time = TimeCurrent();
    
    // 获取当天的夏令时状态
    bool london_dst = GetCachedLondonDST(current_time);
    bool newyork_dst = GetCachedNewYorkDST(current_time);
    
    // 获取当天0点GMT时间作为基准
    datetime today_start = GetTodayStart();
    
    // 计算关键时间点
    int london_open_hour = london_dst ? 7 : 8;
    int london_close_hour = london_dst ? 16 : 17;
    int newyork_open_hour = newyork_dst ? 13 : 14;
    int newyork_close_hour = 22;
    int asia_start_hour = 22;
    
    // 构建时段蓝图数组
    // 1. 亚洲时段
    g_context.session_blueprint[0].name = "亚洲时段";
    g_context.session_blueprint[0].startTimeGMT = today_start - 2*3600; // 前一天22:00
    g_context.session_blueprint[0].endTimeGMT = today_start + london_open_hour*3600;
    g_context.session_blueprint[0].rawEndTimeGMT = g_context.session_blueprint[0].endTimeGMT;
    g_context.session_blueprint[0].sessionColor = ASIA_SESSION_COLOR;
    g_context.session_blueprint[0].sessionEnum = SESSION_ASIA;
    
    // 2. 伦敦时段
    g_context.session_blueprint[1].name = "伦敦时段";
    g_context.session_blueprint[1].startTimeGMT = today_start + london_open_hour*3600;
    g_context.session_blueprint[1].endTimeGMT = today_start + newyork_open_hour*3600 + 30*60;
    g_context.session_blueprint[1].rawEndTimeGMT = g_context.session_blueprint[1].endTimeGMT;
    g_context.session_blueprint[1].sessionColor = LONDON_SESSION_COLOR;
    g_context.session_blueprint[1].sessionEnum = SESSION_LONDON;
    
    // 3. 重叠时段（多方角力）
    g_context.session_blueprint[2].name = "重叠时段(多方角力)";
    g_context.session_blueprint[2].startTimeGMT = today_start + newyork_open_hour*3600 + 30*60;
    g_context.session_blueprint[2].endTimeGMT = today_start + london_close_hour*3600;
    g_context.session_blueprint[2].rawEndTimeGMT = g_context.session_blueprint[2].endTimeGMT;
    g_context.session_blueprint[2].sessionColor = OVERLAP_SESSION_COLOR;
    g_context.session_blueprint[2].sessionEnum = SESSION_OVERLAP;
    
    // 4. 纽约后半段（一方主导）
    g_context.session_blueprint[3].name = "纽约后半段(一方主导)";
    g_context.session_blueprint[3].startTimeGMT = today_start + london_close_hour*3600;
    g_context.session_blueprint[3].endTimeGMT = today_start + newyork_close_hour*3600;
    g_context.session_blueprint[3].rawEndTimeGMT = g_context.session_blueprint[3].endTimeGMT;
    g_context.session_blueprint[3].sessionColor = NEWYORK_SESSION_COLOR;
    g_context.session_blueprint[3].sessionEnum = SESSION_NEWYORK;
    
    // 更新蓝图时间戳
    g_context.last_blueprint_update = current_time;
    
    LogInfo("每日时段蓝图计算完成:", __FUNCTION__);
    for(int i = 0; i < 4; i++)
    {
        LogInfo(StringFormat("  %s: %s - %s", 
                            g_context.session_blueprint[i].name,
                            TimeToString(g_context.session_blueprint[i].startTimeGMT, TIME_MINUTES),
                            TimeToString(g_context.session_blueprint[i].endTimeGMT, TIME_MINUTES)), __FUNCTION__);
    }
}

//+------------------------------------------------------------------+
//| 获取时段剩余时间（秒）                                           |
//+------------------------------------------------------------------+
int GetSessionRemainingTime(ENUM_TRADING_SESSION session)
{
    if(session == SESSION_NONE) return 0;
    
    datetime current_gmt = TimeGMT();
    
    // 查找对应的时段蓝图
    for(int i = 0; i < 4; i++)
    {
        if(g_context.session_blueprint[i].sessionEnum == session)
        {
            if(current_gmt >= g_context.session_blueprint[i].startTimeGMT && 
               current_gmt < g_context.session_blueprint[i].endTimeGMT)
            {
                return (int)(g_context.session_blueprint[i].endTimeGMT - current_gmt);
            }
        }
    }
    
    return 0;
}

//+------------------------------------------------------------------+
//| 格式化剩余时间为字符串                                           |
//+------------------------------------------------------------------+
string FormatRemainingTime(int remaining_seconds)
{
    if(remaining_seconds <= 0) return "已结束";
    
    int hours = remaining_seconds / 3600;
    int minutes = (remaining_seconds % 3600) / 60;
    int seconds = remaining_seconds % 60;
    
    if(hours > 0)
    {
        return StringFormat("%d小时%d分钟", hours, minutes);
    }
    else if(minutes > 0)
    {
        return StringFormat("%d分钟%d秒", minutes, seconds);
    }
    else
    {
        return StringFormat("%d秒", seconds);
    }
}

//+------------------------------------------------------------------+
//| 获取下一个交易时段信息                                           |
//+------------------------------------------------------------------+
string GetNextSessionInfo()
{
    datetime current_gmt = TimeGMT();
    ENUM_TRADING_SESSION current_session = GetCurrentTradingSession();
    
    if(current_session == SESSION_NONE)
    {
        // 当前不在主要时段，查找下一个时段
        for(int i = 0; i < 4; i++)
        {
            if(current_gmt < g_context.session_blueprint[i].startTimeGMT)
            {
                int time_to_next = (int)(g_context.session_blueprint[i].startTimeGMT - current_gmt);
                return StringFormat("下一时段: %s (%s后开始)", 
                                   g_context.session_blueprint[i].name,
                                   FormatRemainingTime(time_to_next));
            }
        }
        
        // 如果没找到今天的下一个时段，说明今天的时段都结束了
        return "今日交易时段已结束";
    }
    else
    {
        // 当前在主要时段，显示剩余时间
        int remaining = GetSessionRemainingTime(current_session);
        return StringFormat("当前时段剩余: %s", FormatRemainingTime(remaining));
    }
}

//+------------------------------------------------------------------+
//| 打印时段详细信息                                                 |
//+------------------------------------------------------------------+
void PrintSessionDetailInfo(string context = "")
{
    LogInfo("========== 时段详细信息 ==========", __FUNCTION__);
    if(context != "") LogInfo(StringFormat("上下文: %s", context), __FUNCTION__);
    
    datetime current_time = TimeCurrent();
    datetime current_gmt = TimeGMT();
    
    LogInfo(StringFormat("当前本地时间: %s", TimeToString(current_time, TIME_DATE|TIME_MINUTES)), __FUNCTION__);
    LogInfo(StringFormat("当前GMT时间: %s", TimeToString(current_gmt, TIME_DATE|TIME_MINUTES)), __FUNCTION__);
    
    // 夏令时状态
    bool london_dst = GetCachedLondonDST(current_time);
    bool newyork_dst = GetCachedNewYorkDST(current_time);
    LogInfo(StringFormat("伦敦夏令时: %s", london_dst ? "是" : "否"), __FUNCTION__);
    LogInfo(StringFormat("纽约夏令时: %s", newyork_dst ? "是" : "否"), __FUNCTION__);
    
    // 当前时段
    ENUM_TRADING_SESSION current_session = GetCurrentTradingSession();
    string session_name = GetSessionName(current_session);
    LogInfo(StringFormat("当前时段: %s", session_name), __FUNCTION__);
    
    // 时段蓝图
    LogInfo("今日时段蓝图:", __FUNCTION__);
    for(int i = 0; i < 4; i++)
    {
        string status = "";
        if(current_gmt >= g_context.session_blueprint[i].startTimeGMT && 
           current_gmt < g_context.session_blueprint[i].endTimeGMT)
        {
            status = " [当前]";
        }
        else if(current_gmt >= g_context.session_blueprint[i].endTimeGMT)
        {
            status = " [已结束]";
        }
        else
        {
            status = " [未开始]";
        }
        
        LogInfo(StringFormat("  %s: %s - %s%s", 
                            g_context.session_blueprint[i].name,
                            TimeToString(g_context.session_blueprint[i].startTimeGMT, TIME_MINUTES),
                            TimeToString(g_context.session_blueprint[i].endTimeGMT, TIME_MINUTES),
                            status), __FUNCTION__);
    }
    
    // 下一时段信息
    string next_info = GetNextSessionInfo();
    LogInfo(next_info, __FUNCTION__);
    
    LogInfo("================================", __FUNCTION__);
}

//+------------------------------------------------------------------+
//| 获取时区偏移信息                                                 |
//+------------------------------------------------------------------+
string GetTimezoneInfo()
{
    datetime local_time = TimeCurrent();
    datetime gmt_time = TimeGMT();
    
    int offset_seconds = (int)(local_time - gmt_time);
    int offset_hours = offset_seconds / 3600;
    
    string timezone_info = StringFormat("本地时区: GMT%+d", offset_hours);
    
    return timezone_info;
}

//+------------------------------------------------------------------+
//| 检查时间数据完整性                                               |
//+------------------------------------------------------------------+
bool ValidateTimeData()
{
    datetime current_time = TimeCurrent();
    datetime gmt_time = TimeGMT();
    
    // 检查时间是否合理
    if(current_time <= 0 || gmt_time <= 0)
    {
        LogError("时间数据无效", __FUNCTION__);
        return false;
    }
    
    // 检查时间差是否合理（应该在-12到+14小时之间）
    int time_diff = (int)(current_time - gmt_time);
    if(MathAbs(time_diff) > 50400) // 14小时 = 50400秒
    {
        LogWarning(StringFormat("时区偏移异常: %d秒", time_diff), __FUNCTION__);
    }
    
    // 检查夏令时缓存是否有效
    if(!g_context.dst_cache.is_valid)
    {
        LogWarning("夏令时缓存无效", __FUNCTION__);
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 强制刷新时间数据                                                 |
//+------------------------------------------------------------------+
void ForceRefreshTimeData()
{
    LogInfo("强制刷新时间数据", __FUNCTION__);
    
    // 清除夏令时缓存
    g_context.dst_cache.is_valid = false;
    
    // 重新计算夏令时状态
    datetime current_time = TimeCurrent();
    GetCachedLondonDST(current_time);
    GetCachedNewYorkDST(current_time);
    
    // 重新计算时段蓝图
    CalculateDailySessionBlueprint();
    
    LogInfo("时间数据刷新完成", __FUNCTION__);
}
