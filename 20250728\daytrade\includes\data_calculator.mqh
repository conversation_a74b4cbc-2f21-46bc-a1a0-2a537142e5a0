//+------------------------------------------------------------------+
//|                                             data_calculator.mqh |
//|                                  Copyright 2025, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, MetaQuotes Ltd."
#property link      "https://www.mql5.com"

#include "constants.mqh"
#include "globals.mqh"
#include "config.mqh"
#include "error_handler.mqh"
#include "time_functions.mqh"

//+------------------------------------------------------------------+
//| 价格变化检测 - 极简变化驱动的核心（性能优化版）                  |
//+------------------------------------------------------------------+
bool DetectPriceChange()
{
    PERFORMANCE_MONITOR();
    
    // 性能优化：减少API调用次数，使用静态变量缓存
    static double s_last_high = 0;
    static double s_last_low = 0;
    static double s_last_close = 0;
    static datetime s_last_check_time = 0;
    static int s_consecutive_no_change = 0;
    
    datetime current_time = TimeCurrent();
    
    // 性能优化：如果距离上次检查时间太短，直接返回缓存结果
    if(current_time - s_last_check_time < 1) // 1秒内不重复检查
    {
        s_consecutive_no_change++;
        if(s_consecutive_no_change > 10) // 连续10次无变化后降低检查频率
        {
            return g_context.price_cache.has_change;
        }
    }
    
    s_last_check_time = current_time;
    
    // 1. 获取当前价格数据
    double current_high = iHigh(_Symbol, PERIOD_D1, 0);
    double current_low = iLow(_Symbol, PERIOD_D1, 0);
    double current_close = iClose(_Symbol, PERIOD_D1, 0);
    
    // 2. 内联数据验证，避免函数调用开销
    if(current_high <= 0 || current_high == EMPTY_VALUE ||
       current_low <= 0 || current_low == EMPTY_VALUE ||
       current_close <= 0 || current_close == EMPTY_VALUE)
    {
        LogError("获取价格数据失败", __FUNCTION__);
        return false;
    }
    
    // 3. 逻辑验证：最高价应该大于等于最低价
    if(current_high < current_low)
    {
        LogError(StringFormat("价格数据异常: 最高价(%f) < 最低价(%f)", current_high, current_low), __FUNCTION__);
        return false;
    }
    
    // 4. 快速变化检测：与静态缓存比较
    bool has_significant_change = false;
    
    if(s_last_high == 0) // 首次运行
    {
        has_significant_change = true;
    }
    else
    {
        // 使用更高效的变化检测
        double high_diff = MathAbs(current_high - s_last_high);
        double low_diff = MathAbs(current_low - s_last_low);
        double close_diff = MathAbs(current_close - s_last_close);
        
        has_significant_change = (high_diff > MIN_PRICE_CHANGE) || 
                                (low_diff > MIN_PRICE_CHANGE) || 
                                (close_diff > MIN_PRICE_CHANGE);
    }
    
    // 5. 更新缓存
    if(has_significant_change)
    {
        // 更新静态缓存
        s_last_high = current_high;
        s_last_low = current_low;
        s_last_close = current_close;
        s_consecutive_no_change = 0;
        
        // 更新全局缓存
        g_context.price_cache.cached_high = current_high;
        g_context.price_cache.cached_low = current_low;
        g_context.price_cache.cached_close = current_close;
        g_context.price_cache.last_check = current_time;
        g_context.price_cache.has_change = true;
        
        // 调试信息（控制输出频率）
        static int change_count = 0;
        change_count++;
        if(change_count % DEBUG_LOG_INTERVAL == 1 || InpEnableDebugLog)
        {
            LogDebug(StringFormat("价格变化检测 #%d - 高:%.5f 低:%.5f 收:%.5f", 
                                 change_count, current_high, current_low, current_close), __FUNCTION__);
        }
        
        return true;
    }
    else
    {
        s_consecutive_no_change++;
        g_context.price_cache.has_change = false;
        return false;
    }
}

//+------------------------------------------------------------------+
//| 获取日均真实波幅（ADTR）- 优化版本                               |
//+------------------------------------------------------------------+
double GetDailyAverageRange()
{
    PERFORMANCE_MONITOR();
    
    double sum = 0;
    int valid_count = 0;
    
    LogDebug(StringFormat("开始计算日均波幅，回溯%d天", InpAvgDays), __FUNCTION__);
    
    // 计算过去N天的真实波幅
    for(int i = 1; i <= InpAvgDays; i++)
    {
        double high = iHigh(_Symbol, PERIOD_D1, i);
        double low = iLow(_Symbol, PERIOD_D1, i);
        double prev_close = iClose(_Symbol, PERIOD_D1, i + 1);
        
        // 数据有效性检查
        if(!ValidatePrice(high, StringFormat("high[%d]", i), __FUNCTION__) ||
           !ValidatePrice(low, StringFormat("low[%d]", i), __FUNCTION__) ||
           !ValidatePrice(prev_close, StringFormat("prev_close[%d]", i), __FUNCTION__))
        {
            LogWarning(StringFormat("第%d天数据无效，跳过", i), __FUNCTION__);
            continue;
        }
        
        // 计算真实波幅：TR = Max((High-Low), abs(High-Close[1]), abs(Low-Close[1]))
        double tr1 = high - low;
        double tr2 = MathAbs(high - prev_close);
        double tr3 = MathAbs(low - prev_close);
        double true_range = MathMax(tr1, MathMax(tr2, tr3));
        
        sum += true_range;
        valid_count++;
        
        // 每10天打印一次进度
        if(i % 10 == 0 && InpEnableDebugLog)
        {
            LogDebug(StringFormat("日均波幅计算进度: %d/%d天, 当前平均: %f", 
                                 i, InpAvgDays, valid_count > 0 ? sum/valid_count : 0), __FUNCTION__);
        }
    }
    
    double result = (valid_count > 0) ? (sum / valid_count) : 0;
    
    LogInfo(StringFormat("日均波幅计算完成: %f (有效天数: %d/%d)", 
                        result, valid_count, InpAvgDays), __FUNCTION__);
    
    return result;
}

//+------------------------------------------------------------------+
//| 获取今日已实现真实波幅 - 增强版本                                |
//+------------------------------------------------------------------+
double GetTodayRange()
{
    PERFORMANCE_MONITOR();
    
    // 方法1：直接获取日线数据
    double high = iHigh(_Symbol, PERIOD_D1, 0);
    double low = iLow(_Symbol, PERIOD_D1, 0);
    double prev_close = iClose(_Symbol, PERIOD_D1, 1);
    
    // 方法2：如果方法1失败，使用分钟线数据重构
    if(!ValidatePrice(high, "today_high", __FUNCTION__) || 
       !ValidatePrice(low, "today_low", __FUNCTION__))
    {
        LogWarning("日线数据获取失败，尝试使用分钟线数据重构", __FUNCTION__);
        
        // 获取今日开盘时间
        datetime today_start = GetTodayStart();
        
        // 在M1周期上查找今日的最高最低价
        double temp_high = 0, temp_low = DBL_MAX;
        bool found_data = false;
        int bars_checked = 0;
        
        for(int i = 0; i < MAX_BARS_TO_CHECK; i++)
        {
            datetime bar_time = iTime(_Symbol, PERIOD_M1, i);
            if(bar_time < today_start) break; // 超出今日范围
            
            double bar_high = iHigh(_Symbol, PERIOD_M1, i);
            double bar_low = iLow(_Symbol, PERIOD_M1, i);
            
            if(ValidatePrice(bar_high, "", "") && ValidatePrice(bar_low, "", ""))
            {
                if(bar_high > temp_high) temp_high = bar_high;
                if(bar_low < temp_low) temp_low = bar_low;
                found_data = true;
                bars_checked++;
            }
        }
        
        if(found_data)
        {
            high = temp_high;
            low = temp_low;
            LogInfo(StringFormat("使用分钟线重构今日价格数据成功，检查了%d根K线", bars_checked), __FUNCTION__);
        }
        else
        {
            LogError("无法获取今日价格数据", __FUNCTION__);
            return 0;
        }
    }
    
    // 获取昨日收盘价
    if(!ValidatePrice(prev_close, "prev_close", __FUNCTION__))
    {
        prev_close = iClose(_Symbol, PERIOD_D1, 1);
        if(!ValidatePrice(prev_close, "prev_close_retry", __FUNCTION__))
        {
            // 如果还是获取不到，使用今日开盘价作为近似值
            prev_close = iOpen(_Symbol, PERIOD_D1, 0);
            LogWarning("使用今日开盘价替代昨日收盘价", __FUNCTION__);
        }
    }
    
    // 最终数据验证
    if(!ValidatePrice(high, "final_high", __FUNCTION__) || 
       !ValidatePrice(low, "final_low", __FUNCTION__) || 
       !ValidatePrice(prev_close, "final_prev_close", __FUNCTION__))
    {
        LogError("今日波幅计算失败：价格数据无效", __FUNCTION__);
        return 0;
    }
    
    // 计算今日真实波幅
    double tr1 = high - low;
    double tr2 = MathAbs(high - prev_close);
    double tr3 = MathAbs(low - prev_close);
    double result = MathMax(tr1, MathMax(tr2, tr3));
    
    // 调试信息（控制输出频率）
    static int calc_count = 0;
    calc_count++;
    if(calc_count % DEBUG_LOG_INTERVAL == 1 || InpEnableDebugLog)
    {
        LogDebug(StringFormat("今日波幅计算 #%d - 最高:%f 最低:%f 昨收:%f TR:%f", 
                             calc_count, high, low, prev_close, result), __FUNCTION__);
    }
    
    return result;
}

//+------------------------------------------------------------------+
//| 获取指定日期和时段的波幅（动态版本）- 性能优化版                 |
//+------------------------------------------------------------------+
double CalculateSessionDailyRangeDynamic(datetime day_start, int start_hour, int end_hour)
{
    PERFORMANCE_MONITOR();
    
    if(!ValidateDateTime(day_start, "day_start", __FUNCTION__))
        return 0;
    
    // 处理跨日情况（如亚洲时段从前一天22:00开始）
    datetime session_start_time = day_start + start_hour * 3600;
    datetime session_end_time = day_start + end_hour * 3600;
    
    if(start_hour > end_hour) // 跨日情况
    {
        session_start_time = day_start - (24 - start_hour) * 3600; // 前一天的时间
    }
    
    LogDebug(StringFormat("计算时段波幅: %s - %s", 
                         TimeToString(session_start_time, TIME_MINUTES),
                         TimeToString(session_end_time, TIME_MINUTES)), __FUNCTION__);
    
    // 性能优化：预分配数组大小，避免动态扩展
    static double s_highs_buffer[500];  // 静态缓冲区，避免频繁分配
    static double s_lows_buffer[500];
    
    int start_bar = iBarShift(_Symbol, PERIOD_M15, session_start_time);
    int end_bar = iBarShift(_Symbol, PERIOD_M15, session_end_time);
    
    if(start_bar < 0 || end_bar < 0 || start_bar <= end_bar)
    {
        LogWarning(StringFormat("无法获取时段K线索引: start_bar=%d, end_bar=%d", start_bar, end_bar), __FUNCTION__);
        return 0;
    }
    
    int bars_count = MathMin(start_bar - end_bar + 1, 500); // 限制最大处理数量
    
    // 使用静态缓冲区进行批量获取
    if(CopyHigh(_Symbol, PERIOD_M15, end_bar, bars_count, s_highs_buffer) == bars_count &&
       CopyLow(_Symbol, PERIOD_M15, end_bar, bars_count, s_lows_buffer) == bars_count)
    {
        // 数组大小验证
        if(bars_count <= 0)
        {
            LogError("获取的K线数量为0", __FUNCTION__);
            return 0;
        }
        
        // 初始化极值
        double session_high = s_highs_buffer[0];
        double session_low = s_lows_buffer[0];
        int valid_bars = 0;
        
        // 批量处理，减少函数调用开销
        for(int i = 0; i < bars_count; i++)
        {
            // 内联验证，避免函数调用开销
            if(s_highs_buffer[i] > 0 && s_highs_buffer[i] != EMPTY_VALUE && 
               s_lows_buffer[i] > 0 && s_lows_buffer[i] != EMPTY_VALUE)
            {
                if(s_highs_buffer[i] > session_high) session_high = s_highs_buffer[i];
                if(s_lows_buffer[i] < session_low) session_low = s_lows_buffer[i];
                valid_bars++;
            }
        }
        
        if(valid_bars > 0)
        {
            double result = session_high - session_low;
            LogDebug(StringFormat("时段波幅计算成功: %f (有效K线:%d/%d)", result, valid_bars, bars_count), __FUNCTION__);
            return result;
        }
        else
        {
            LogWarning("没有有效的价格数据", __FUNCTION__);
            return 0;
        }
    }
    else
    {
        LogError("批量获取K线数据失败", __FUNCTION__);
        return 0;
    }
}

//+------------------------------------------------------------------+
//| 计算时段统计数据（v12重构版本 - 纽约时段拆分逻辑）               |
//+------------------------------------------------------------------+
void CalculateSessionStatistics()
{
    PERFORMANCE_MONITOR();
    
    LogInfo("开始计算时段统计数据（纽约时段拆分版）...", __FUNCTION__);
    
    // 初始化统计变量
    double asia_sum = 0, london_sum = 0, overlap_sum = 0, newyork_sum = 0;
    int valid_days = 0;
    
    // 循环过去N天
    for(int day = 1; day <= InpAvgDays; day++)
    {
        datetime day_start = iTime(_Symbol, PERIOD_D1, day);
        if(!ValidateDateTime(day_start, StringFormat("day_start[%d]", day), __FUNCTION__))
        {
            LogWarning(StringFormat("第%d天数据无效，跳过", day), __FUNCTION__);
            continue;
        }
        
        // 为每一天计算动态时段时间
        bool london_dst = IsLondonInDST(day_start);
        bool newyork_dst = IsNewYorkInDST(day_start);
        
        // v12关键时间点计算（小时）
        int london_open_hour = london_dst ? 7 : 8;                    // 伦敦开盘
        int london_close_hour = london_dst ? 16 : 17;                 // 伦敦收盘
        int newyork_open_hour = newyork_dst ? 13 : 14;                // 纽约开盘（简化为整点）
        int newyork_close_hour = 22;                                  // 纽约收盘
        int asia_start_hour = 22;                                     // 亚洲开始（前一天）
        
        // v12新的时段定义：
        // 1. 亚洲时段：前一天22:00到伦敦开盘
        double asia_range = CalculateSessionDailyRangeDynamic(day_start, asia_start_hour, london_open_hour);
        
        // 2. 伦敦时段：伦敦开盘到纽约开盘
        double london_range = CalculateSessionDailyRangeDynamic(day_start, london_open_hour, newyork_open_hour);
        
        // 3. 重叠时段：纽约开盘到伦敦收盘（多方角力）
        double overlap_range = 0;
        if(newyork_open_hour < london_close_hour)
        {
            overlap_range = CalculateSessionDailyRangeDynamic(day_start, newyork_open_hour, london_close_hour);
            
            // 调试信息：每5天打印一次重叠时段详情
            if(day % 5 == 1 && InpEnableDebugLog)
            {
                LogDebug(StringFormat("第%d天重叠时段: %d:00-%d:00 GMT, 波幅:%f", 
                                     day, newyork_open_hour, london_close_hour, overlap_range), __FUNCTION__);
            }
        }
        else
        {
            LogWarning(StringFormat("第%d天无重叠时段: 纽约%d点 >= 伦敦收盘%d点", 
                                   day, newyork_open_hour, london_close_hour), __FUNCTION__);
        }
        
        // 4. 纽约后半段：伦敦收盘到纽约收盘（一方主导）
        double newyork_range = CalculateSessionDailyRangeDynamic(day_start, london_close_hour, newyork_close_hour);
        
        // 累加有效数据
        if(asia_range > 0) asia_sum += asia_range;
        if(london_range > 0) london_sum += london_range;
        if(overlap_range > 0) overlap_sum += overlap_range;
        if(newyork_range > 0) newyork_sum += newyork_range;
        
        valid_days++;
        
        // 每5天打印一次进度
        if(day % 5 == 0)
        {
            LogInfo(StringFormat("时段统计进度: %d/%d天", day, InpAvgDays), __FUNCTION__);
        }
    }
    
    // 计算平均值并更新全局上下文
    g_context.session_stats.asia_avg_range = (valid_days > 0) ? (asia_sum / valid_days) : 0;
    g_context.session_stats.london_avg_range = (valid_days > 0) ? (london_sum / valid_days) : 0;
    g_context.session_stats.overlap_avg_range = (valid_days > 0) ? (overlap_sum / valid_days) : 0;
    g_context.session_stats.newyork_avg_range = (valid_days > 0) ? (newyork_sum / valid_days) : 0;
    g_context.session_stats.last_update = TimeCurrent();
    g_context.session_stats.is_valid = true;
    
    LogInfo("时段统计数据计算完成（纽约时段拆分版）:", __FUNCTION__);
    LogInfo(StringFormat("  亚洲时段: %f", g_context.session_stats.asia_avg_range), __FUNCTION__);
    LogInfo(StringFormat("  伦敦时段: %f", g_context.session_stats.london_avg_range), __FUNCTION__);
    LogInfo(StringFormat("  重叠时段(多方角力): %f", g_context.session_stats.overlap_avg_range), __FUNCTION__);
    LogInfo(StringFormat("  纽约后半段(一方主导): %f", g_context.session_stats.newyork_avg_range), __FUNCTION__);
    LogInfo(StringFormat("  有效天数: %d/%d", valid_days, InpAvgDays), __FUNCTION__);
}

//+------------------------------------------------------------------+
//| 获取当前时段从开始到现在的波幅（v12重构版本）- 性能优化版        |
//+------------------------------------------------------------------+
double GetCurrentSessionRangeSoFar()
{
    PERFORMANCE_MONITOR();
    
    ENUM_TRADING_SESSION current_session = GetCurrentTradingSession();
    if(current_session == SESSION_NONE) 
    {
        LogDebug("当前非主要时段，返回0", __FUNCTION__);
        return 0;
    }
    
    datetime current_time = TimeCurrent();
    datetime current_gmt = TimeGMT();
    
    // 获取当天的夏令时状态（使用缓存）
    bool london_dst = GetCachedLondonDST(current_time);
    bool newyork_dst = GetCachedNewYorkDST(current_time);
    
    // 获取当天0点GMT时间作为基准
    datetime today_start = GetTodayStart();
    
    // v12关键时间点计算
    datetime london_open = today_start + (london_dst ? 7*3600 : 8*3600);      // 伦敦开盘
    datetime london_close = today_start + (london_dst ? 16*3600 : 17*3600);   // 伦敦收盘
    datetime newyork_open = today_start + (newyork_dst ? 13*3600 + 30*60 : 14*3600 + 30*60); // 纽约开盘
    datetime asia_start = today_start - 2*3600;                               // 亚洲开始
    
    // 根据当前时段确定开始时间
    datetime session_start_time = 0;
    string session_name = "";
    
    switch(current_session)
    {
        case SESSION_ASIA:
            session_start_time = asia_start;
            session_name = "亚洲时段";
            break;
        case SESSION_LONDON:
            session_start_time = london_open;
            session_name = "伦敦时段";
            break;
        case SESSION_OVERLAP:
            session_start_time = newyork_open;  // v12关键：重叠时段从纽约开盘开始
            session_name = "重叠时段(多方角力)";
            break;
        case SESSION_NEWYORK:
            session_start_time = london_close;  // v12关键：纽约后半段从伦敦收盘开始
            session_name = "纽约后半段(一方主导)";
            break;
        default:
            LogError("未知时段类型", __FUNCTION__);
            return 0;
    }
    
    LogDebug(StringFormat("计算%s波幅: %s - %s", 
                         session_name,
                         TimeToString(session_start_time, TIME_MINUTES), 
                         TimeToString(current_gmt, TIME_MINUTES)), __FUNCTION__);
    
    // 性能优化：使用静态缓冲区，避免频繁内存分配
    static double s_session_highs[1000];  // 静态缓冲区
    static double s_session_lows[1000];
    
    int start_bar = iBarShift(_Symbol, PERIOD_M1, session_start_time);
    int end_bar = iBarShift(_Symbol, PERIOD_M1, current_gmt);
    
    if(start_bar < 0 || end_bar < 0 || start_bar <= end_bar) 
    {
        LogWarning(StringFormat("K线索引异常: start_bar=%d, end_bar=%d", start_bar, end_bar), __FUNCTION__);
        return 0;
    }
    
    int bars_count = MathMin(start_bar - end_bar + 1, 1000); // 限制最大处理数量
    
    // 使用静态缓冲区批量获取高低点数据
    if(CopyHigh(_Symbol, PERIOD_M1, end_bar, bars_count, s_session_highs) == bars_count &&
       CopyLow(_Symbol, PERIOD_M1, end_bar, bars_count, s_session_lows) == bars_count)
    {
        // 数组大小验证
        if(bars_count <= 0)
        {
            LogError("获取的K线数量为0", __FUNCTION__);
            return 0;
        }
        
        // 初始化极值 - 使用第一个有效值
        double session_high = s_session_highs[0];
        double session_low = s_session_lows[0];
        int valid_bars = 0;
        
        // 找到第一个有效值作为初始值
        for(int i = 0; i < bars_count; i++)
        {
            if(s_session_highs[i] > 0 && s_session_highs[i] != EMPTY_VALUE && 
               s_session_lows[i] > 0 && s_session_lows[i] != EMPTY_VALUE)
            {
                session_high = s_session_highs[i];
                session_low = s_session_lows[i];
                valid_bars = 1;
                break;
            }
        }
        
        if(valid_bars == 0)
        {
            LogWarning("没有找到有效的初始价格数据", __FUNCTION__);
            return 0;
        }
        
        // 批量处理，减少函数调用开销
        for(int i = 1; i < bars_count; i++)
        {
            // 内联验证，避免函数调用开销
            if(s_session_highs[i] > 0 && s_session_highs[i] != EMPTY_VALUE && 
               s_session_lows[i] > 0 && s_session_lows[i] != EMPTY_VALUE)
            {
                if(s_session_highs[i] > session_high) session_high = s_session_highs[i];
                if(s_session_lows[i] < session_low) session_low = s_session_lows[i];
                valid_bars++;
            }
        }
        
        double result = session_high - session_low;
        LogDebug(StringFormat("%s当前波幅: %f (有效K线:%d/%d)", session_name, result, valid_bars, bars_count), __FUNCTION__);
        return result;
    }
    else
    {
        LogError("批量获取时段K线数据失败", __FUNCTION__);
        return 0;
    }
}

//+------------------------------------------------------------------+
//| 实时计算更新函数（仅在有变化时调用）                             |
//+------------------------------------------------------------------+
void UpdateRealTimeCalculations()
{
    PERFORMANCE_MONITOR();
    
    LogDebug("开始更新实时计算数据", __FUNCTION__);
    
    // 1. 更新时段信息
    ENUM_TRADING_SESSION new_session = GetCurrentTradingSession();
    string new_session_name = GetSessionName(new_session);
    
    if(new_session != g_context.current_session || new_session_name != g_context.current_session_name)
    {
        LogInfo(StringFormat("时段变化: '%s' -> '%s'", g_context.current_session_name, new_session_name), __FUNCTION__);
    }
    
    g_context.current_session = new_session;
    g_context.current_session_name = new_session_name;
    
    // 2. 重新计算今日波幅
    g_context.today_range = GetTodayRange();
    
    // 3. 重新计算预估剩余和百分比
    if(ValidateDivision(g_context.daily_avg_range, "daily_avg_range", __FUNCTION__))
    {
        g_context.remaining_range = g_context.daily_avg_range - g_context.today_range;
        g_context.percentage = SafeDivision(g_context.today_range, g_context.daily_avg_range, 0.0, __FUNCTION__) * 100;
    }
    else
    {
        g_context.remaining_range = 0;
        g_context.percentage = 0;
    }
    
    LogDebug(StringFormat("实时计算更新完成 - 今日波幅:%f, 剩余:%f, 百分比:%f%%", 
                         g_context.today_range, g_context.remaining_range, g_context.percentage), __FUNCTION__);
}

//+------------------------------------------------------------------+
//| 更新时段波动率计算（v12重构版 - 纽约时段拆分分析）               |
//+------------------------------------------------------------------+
void UpdateSessionVolatilityCalculations()
{
    PERFORMANCE_MONITOR();
    
    LogDebug("开始更新时段内波动率分析（纽约时段拆分版）", __FUNCTION__);
    
    // 获取当前时段的枚举值
    ENUM_TRADING_SESSION current_session_enum = GetCurrentTradingSession();
    
    // 根据当前时段，从时段统计中查找对应的历史均幅
    switch(current_session_enum)
    {
        case SESSION_ASIA:
            g_context.current_session_avg_range = g_context.session_stats.asia_avg_range;
            LogDebug(StringFormat("使用亚洲时段均幅: %f", g_context.current_session_avg_range), __FUNCTION__);
            break;
        case SESSION_LONDON:
            g_context.current_session_avg_range = g_context.session_stats.london_avg_range;
            LogDebug(StringFormat("使用伦敦时段均幅: %f", g_context.current_session_avg_range), __FUNCTION__);
            break;
        case SESSION_OVERLAP:
            g_context.current_session_avg_range = g_context.session_stats.overlap_avg_range;
            LogDebug(StringFormat("使用重叠时段均幅(多方角力): %f", g_context.current_session_avg_range), __FUNCTION__);
            break;
        case SESSION_NEWYORK:
            g_context.current_session_avg_range = g_context.session_stats.newyork_avg_range;
            LogDebug(StringFormat("使用纽约后半段均幅(一方主导): %f", g_context.current_session_avg_range), __FUNCTION__);
            break;
        default:
            g_context.current_session_avg_range = 0;
            LogDebug("非主要时段，均幅设为0", __FUNCTION__);
            break;
    }
    
    // 调用GetCurrentSessionRangeSoFar函数获取今日现幅
    g_context.current_session_today_range = GetCurrentSessionRangeSoFar();
    
    // 计算剩余空间和完成进度
    if(ValidateDivision(g_context.current_session_avg_range, "current_session_avg_range", __FUNCTION__))
    {
        g_context.current_session_remaining_range = g_context.current_session_avg_range - g_context.current_session_today_range;
        g_context.current_session_percentage = SafeDivision(g_context.current_session_today_range, g_context.current_session_avg_range, 0.0, __FUNCTION__) * 100;
    }
    else
    {
        g_context.current_session_remaining_range = 0;
        g_context.current_session_percentage = 0;
    }
    
    LogDebug(StringFormat("时段波动率分析完成 - 当前时段:%s, 历史均幅:%f, 今日现幅:%f, 剩余空间:%f, 完成进度:%f%%", 
                         GetSessionName(current_session_enum),
                         g_context.current_session_avg_range,
                         g_context.current_session_today_range,
                         g_context.current_session_remaining_range,
                         g_context.current_session_percentage), __FUNCTION__);
}

// UpdateKeyLevelPrices和IsNewDayForKeyLevels函数已在time_functions.mqh中定义，此处移除重复定义

//+------------------------------------------------------------------+
//| 数据计算模块初始化                                               |
//+------------------------------------------------------------------+
void InitializeDataCalculator()
{
    LogInfo("数据计算模块初始化开始...", __FUNCTION__);
    
    // 初始化价格变化缓存
    g_context.price_cache.cached_high = 0;
    g_context.price_cache.cached_low = 0;
    g_context.price_cache.cached_close = 0;
    g_context.price_cache.last_check = 0;
    g_context.price_cache.has_change = false;
    
    // 初始化计算结果
    g_context.daily_avg_range = 0;
    g_context.today_range = 0;
    g_context.remaining_range = 0;
    g_context.percentage = 0;
    
    g_context.current_session_avg_range = 0;
    g_context.current_session_today_range = 0;
    g_context.current_session_remaining_range = 0;
    g_context.current_session_percentage = 0;
    
    // 初始化关键价位
    g_context.daily_open_price = 0;
    g_context.prev_high_price = 0;
    g_context.prev_low_price = 0;
    g_context.key_levels_update = 0;
    
    // 首次计算日均波幅
    g_context.daily_avg_range = GetDailyAverageRange();
    g_context.last_daily_calc = TimeCurrent();
    
    // 首次计算时段统计（如果启用）
    if(InpShowVolatility)
    {
        CalculateSessionStatistics();
    }
    
    // 首次更新关键价位
    UpdateKeyLevelPrices();
    
    LogInfo("数据计算模块初始化完成", __FUNCTION__);
}

//+------------------------------------------------------------------+
//| 数据计算模块清理                                                 |
//+------------------------------------------------------------------+
void CleanupDataCalculator()
{
    LogInfo("数据计算模块清理完成", __FUNCTION__);
}

//+------------------------------------------------------------------+
//| 强制刷新所有计算数据                                             |
//+------------------------------------------------------------------+
void ForceRefreshCalculationData()
{
    PERFORMANCE_MONITOR();
    
    LogInfo("开始强制刷新所有计算数据", __FUNCTION__);
    
    // 清空价格变化检测缓存，强制触发变化检测
    g_context.price_cache.cached_high = 0;
    g_context.price_cache.cached_low = 0;
    g_context.price_cache.cached_close = 0;
    g_context.price_cache.has_change = false;
    
    // 重置时间戳
    g_context.last_data_update = 0;
    g_context.last_backup_update = 0;
    g_context.last_daily_calc = 0;
    
    // 重新计算日均波幅
    g_context.daily_avg_range = GetDailyAverageRange();
    g_context.last_daily_calc = TimeCurrent();
    
    // 重新计算时段统计
    if(InpShowVolatility)
    {
        CalculateSessionStatistics();
    }
    
    // 强制触发变化检测
    DetectPriceChange();
    
    // 重新计算实时数据
    UpdateRealTimeCalculations();
    
    // 重新计算时段波动率
    UpdateSessionVolatilityCalculations();
    
    // 更新关键价位
    UpdateKeyLevelPrices();
    
    // 更新时间戳
    g_context.last_data_update = TimeCurrent();
    g_context.last_backup_update = TimeCurrent();
    
    LogInfo("强制刷新所有计算数据完成", __FUNCTION__);
}
