//+------------------------------------------------------------------+
//|                                                     daytrade.mq5 |
//|                                  Copyright 2025, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "交易时段实时仪表盘 + EMA 5线指标 - 模块化重构版"
#property indicator_chart_window
#property indicator_buffers 5
#property indicator_plots   5

// 包含所有模块
#include "includes/constants.mqh"
#include "includes/config.mqh"
#include "includes/globals.mqh"
#include "includes/error_handler.mqh"
#include "includes/time_functions.mqh"
#include "includes/data_calculator.mqh"
#include "includes/ema_logic.mqh"
#include "includes/ui_dashboard.mqh"

//+------------------------------------------------------------------+
//| 指标初始化函数                                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    LogInfo("=== 交易时段仪表盘 + EMA指标启动 ===", __FUNCTION__);
    LogInfo("版本: v1.0 模块化重构版", __FUNCTION__);
    
    // 1. 初始化全局上下文
    if(!InitializeGlobalContext())
    {
        LogError("全局上下文初始化失败", __FUNCTION__);
        return INIT_FAILED;
    }
    
    // 2. 验证配置参数
    if(!ValidateConfiguration())
    {
        LogError("配置参数验证失败", __FUNCTION__);
        return INIT_PARAMETERS_INCORRECT;
    }
    
    // 3. 初始化时间管理模块
    if(!InitializeTimeModule())
    {
        LogError("时间管理模块初始化失败", __FUNCTION__);
        return INIT_FAILED;
    }
    
    // 4. 初始化EMA指标
    if(!InitializeEMAIndicators())
    {
        LogError("EMA指标初始化失败", __FUNCTION__);
        return INIT_FAILED;
    }
    
    // 5. 设置EMA缓冲区
    if(!SetupEMABuffers())
    {
        LogError("EMA缓冲区设置失败", __FUNCTION__);
        return INIT_FAILED;
    }
    
    // 6. 验证EMA配置
    if(!ValidateEMAConfiguration())
    {
        LogError("EMA配置验证失败", __FUNCTION__);
        return INIT_FAILED;
    }
    
    // 7. 初始化仪表盘（如果启用）
    if(InpShowDashboard)
    {
        if(!InitializeDashboard())
        {
            LogWarning("仪表盘初始化失败，但不影响指标运行", __FUNCTION__);
        }
    }
    
    // 8. 设置指标属性
    SetupIndicatorProperties();
    
    // 9. 执行初始数据计算
    PerformInitialCalculations();
    
    LogInfo("指标初始化完成", __FUNCTION__);
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| 指标反初始化函数                                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    LogInfo("=== 指标反初始化开始 ===", __FUNCTION__);
    LogInfo(StringFormat("反初始化原因: %s", GetDeinitReasonText(reason)), __FUNCTION__);
    
    // 1. 清理仪表盘
    if(InpShowDashboard)
    {
        CleanupDashboardModule();
    }
    
    // 2. 清理EMA模块
    CleanupEMAModule();
    
    // 3. 清理时间模块
    CleanupTimeModule();
    
    // 4. 清理全局上下文
    CleanupGlobalContext();
    
    LogInfo("指标反初始化完成", __FUNCTION__);
}

//+------------------------------------------------------------------+
//| 指标计算函数                                                     |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    PERFORMANCE_MONITOR();
    
    // 1. 数据有效性检查
    if(rates_total < MIN_BARS_FOR_CALCULATION)
    {
        LogDebug(StringFormat("数据不足，需要%d根K线，当前%d根", 
                             MIN_BARS_FOR_CALCULATION, rates_total), __FUNCTION__);
        return 0;
    }
    
    // 2. 检测价格变化（优化性能）
    static datetime last_check_time = 0;
    datetime current_time = TimeCurrent();
    
    if(current_time - last_check_time >= UPDATE_INTERVAL)
    {
        if(!DetectPriceChange())
        {
            // 无价格变化，跳过计算
            return prev_calculated;
        }
        last_check_time = current_time;
    }
    
    // 3. 计算EMA数据
    if(!CalculateEMAData(rates_total, prev_calculated))
    {
        LogWarning("EMA数据计算失败", __FUNCTION__);
        return prev_calculated;
    }
    
    // 4. 更新EMA统计信息
    UpdateEMAStatistics();
    
    // 5. 更新实时计算数据
    UpdateRealTimeCalculations();
    
    // 6. 更新仪表盘显示（如果启用）
    if(InpShowDashboard)
    {
        SmartUpdateDashboard();
    }
    
    // 7. 性能监控和日志（控制频率）
    static int calc_count = 0;
    calc_count++;
    
    if(calc_count % DEBUG_LOG_INTERVAL == 1 || InpEnableDebugLog)
    {
        LogDebug(StringFormat("计算完成 #%d - K线数:%d 前次:%d", 
                             calc_count, rates_total, prev_calculated), __FUNCTION__);
    }
    
    return rates_total;
}

//+------------------------------------------------------------------+
//| 图表事件处理函数                                                 |
//+------------------------------------------------------------------+
void OnChartEvent(const int id,
                  const long &lparam,
                  const double &dparam,
                  const string &sparam)
{
    // 处理图表事件，如鼠标点击、键盘输入等
    switch(id)
    {
        case CHARTEVENT_OBJECT_CLICK:
            // 处理对象点击事件
            if(StringFind(sparam, DASHBOARD_PREFIX) == 0)
            {
                LogDebug(StringFormat("仪表盘对象被点击: %s", sparam), __FUNCTION__);
            }
            break;
            
        case CHARTEVENT_CHART_CHANGE:
            // 图表变化时重新定位仪表盘
            if(InpShowDashboard && g_dashboard.is_created)
            {
                ForceRefreshDashboard();
            }
            break;
            
        default:
            break;
    }
}

//+------------------------------------------------------------------+
//| 设置指标属性                                                     |
//+------------------------------------------------------------------+
void SetupIndicatorProperties()
{
    LogDebug("设置指标属性", __FUNCTION__);
    
    // 设置指标名称
    IndicatorSetString(INDICATOR_SHORTNAME, "交易时段仪表盘+EMA5线");
    
    // 设置指标精度
    IndicatorSetInteger(INDICATOR_DIGITS, _Digits);
    
    // 设置指标级别数量
    IndicatorSetInteger(INDICATOR_LEVELS, 0);
    
    LogDebug("指标属性设置完成", __FUNCTION__);
}

//+------------------------------------------------------------------+
//| 执行初始数据计算                                                 |
//+------------------------------------------------------------------+
void PerformInitialCalculations()
{
    LogInfo("开始执行初始数据计算", __FUNCTION__);
    
    // 1. 计算日均波幅
    g_context.daily_avg_range = GetDailyAverageRange();
    LogInfo(StringFormat("日均波幅: %f", g_context.daily_avg_range), __FUNCTION__);
    
    // 2. 计算今日波幅
    g_context.today_range = GetTodayRange();
    LogInfo(StringFormat("今日波幅: %f", g_context.today_range), __FUNCTION__);
    
    // 3. 计算时段统计数据
    CalculateSessionStatistics();
    
    // 4. 更新当前时段信息
    g_context.current_session = GetCurrentTradingSession();
    g_context.current_session_name = GetSessionName(g_context.current_session);
    LogInfo(StringFormat("当前时段: %s", g_context.current_session_name), __FUNCTION__);
    
    // 5. 计算剩余波幅和百分比
    if(ValidateDivision(g_context.daily_avg_range, "daily_avg_range", __FUNCTION__))
    {
        g_context.remaining_range = g_context.daily_avg_range - g_context.today_range;
        g_context.percentage = SafeDivision(g_context.today_range, g_context.daily_avg_range, 0.0, __FUNCTION__) * 100;
    }
    
    LogInfo(StringFormat("初始计算完成 - 剩余波幅:%f 完成度:%.1f%%", 
                        g_context.remaining_range, g_context.percentage), __FUNCTION__);
}

//+------------------------------------------------------------------+
//| 获取反初始化原因文本                                             |
//+------------------------------------------------------------------+
string GetDeinitReasonText(int reason)
{
    switch(reason)
    {
        case REASON_PROGRAM:     return "程序终止";
        case REASON_REMOVE:      return "从图表移除";
        case REASON_RECOMPILE:   return "重新编译";
        case REASON_CHARTCHANGE: return "图表变化";
        case REASON_CHARTCLOSE:  return "图表关闭";
        case REASON_PARAMETERS:  return "参数变化";
        case REASON_ACCOUNT:     return "账户变化";
        case REASON_TEMPLATE:    return "模板变化";
        case REASON_INITFAILED:  return "初始化失败";
        case REASON_CLOSE:       return "终端关闭";
        default:                 return StringFormat("未知原因(%d)", reason);
    }
}

// ValidateConfiguration函数已在config.mqh中定义，此处移除重复定义

//+------------------------------------------------------------------+
//| 指标自检函数                                                     |
//+------------------------------------------------------------------+
bool PerformSelfCheck()
{
    LogInfo("=== 开始指标自检 ===", __FUNCTION__);
    
    bool all_ok = true;
    
    // 1. 检查全局上下文
    if(!ValidateGlobalContext())
    {
        LogError("全局上下文检查失败", __FUNCTION__);
        all_ok = false;
    }
    
    // 2. 检查EMA数据完整性
    if(!ValidateEMADataIntegrity())
    {
        LogError("EMA数据完整性检查失败", __FUNCTION__);
        all_ok = false;
    }
    
    // 3. 检查仪表盘状态
    if(InpShowDashboard)
    {
        if(!DashboardSelfCheck())
        {
            LogError("仪表盘自检失败", __FUNCTION__);
            all_ok = false;
        }
    }
    
    // 4. 检查时间模块
    if(!TimeModuleSelfCheck())
    {
        LogError("时间模块自检失败", __FUNCTION__);
        all_ok = false;
    }
    
    if(all_ok)
    {
        LogInfo("指标自检通过", __FUNCTION__);
    }
    else
    {
        LogError("指标自检失败", __FUNCTION__);
    }
    
    LogInfo("=== 指标自检完成 ===", __FUNCTION__);
    return all_ok;
}

//+------------------------------------------------------------------+
//| 获取指标状态信息                                                 |
//+------------------------------------------------------------------+
string GetIndicatorStatusInfo()
{
    string status = "=== 指标状态信息 ===\n";
    
    status += StringFormat("指标名称: %s\n", "交易时段仪表盘+EMA5线");
    status += StringFormat("版本: %s\n", "v1.0 模块化重构版");
    status += StringFormat("当前时间: %s\n", TimeToString(TimeCurrent(), TIME_MINUTES));
    status += StringFormat("GMT时间: %s\n", TimeToString(TimeGMT(), TIME_MINUTES));
    status += "\n";
    
    // 时段信息
    status += StringFormat("当前时段: %s\n", g_context.current_session_name);
    status += StringFormat("今日波幅: %.0f点\n", g_context.today_range * MathPow(10, _Digits));
    status += StringFormat("日均波幅: %.0f点\n", g_context.daily_avg_range * MathPow(10, _Digits));
    status += StringFormat("完成度: %.1f%%\n", g_context.percentage);
    status += "\n";
    
    // EMA信息
    status += StringFormat("EMA排列: %s\n", GetEMAAlignmentText());
    if(g_context.ema_support != EMPTY_VALUE)
        status += StringFormat("EMA支撑: %.5f\n", g_context.ema_support);
    if(g_context.ema_resistance != EMPTY_VALUE)
        status += StringFormat("EMA阻力: %.5f\n", g_context.ema_resistance);
    status += "\n";
    
    // 系统状态
    status += StringFormat("仪表盘: %s\n", InpShowDashboard ? (g_dashboard.is_created ? "正常" : "异常") : "禁用");
    status += StringFormat("调试日志: %s\n", InpEnableDebugLog ? "启用" : "禁用");
    status += StringFormat("启用EMA数量: %d\n", GetActiveEMACount());
    
    return status;
}

//+------------------------------------------------------------------+
//| 紧急修复函数                                                     |
//+------------------------------------------------------------------+
void EmergencyFix()
{
    LogWarning("=== 执行紧急修复 ===", __FUNCTION__);
    
    // 1. 重建仪表盘
    if(InpShowDashboard)
    {
        if(!ValidateDashboardIntegrity())
        {
            LogWarning("检测到仪表盘损坏，尝试重建", __FUNCTION__);
            RebuildDashboard();
        }
    }
    
    // 2. 重新初始化EMA
    if(!ValidateEMADataIntegrity())
    {
        LogWarning("检测到EMA数据异常，尝试重新初始化", __FUNCTION__);
        CleanupEMAModule();
        InitializeEMAIndicators();
    }
    
    // 3. 强制刷新所有数据
    ForceRefreshAllData();
    
    LogWarning("紧急修复完成", __FUNCTION__);
}

//+------------------------------------------------------------------+
//| 强制刷新所有数据                                                 |
//+------------------------------------------------------------------+
void ForceRefreshAllData()
{
    LogInfo("强制刷新所有数据", __FUNCTION__);
    
    // 1. 刷新价格缓存
    g_context.price_cache.has_change = true;
    
    // 2. 重新计算所有数据
    PerformInitialCalculations();
    
    // 3. 刷新EMA数据
    ForceRefreshEMAData();
    
    // 4. 刷新仪表盘
    if(InpShowDashboard)
    {
        ForceRefreshDashboard();
    }
    
    LogInfo("所有数据刷新完成", __FUNCTION__);
}

//+------------------------------------------------------------------+
//| 获取性能统计信息                                                 |
//+------------------------------------------------------------------+
string GetPerformanceStats()
{
    string stats = "=== 性能统计信息 ===\n";
    
    stats += StringFormat("总计算次数: %d\n", g_performance_stats.total_calculations);
    stats += StringFormat("平均计算时间: %.2fms\n", g_performance_stats.avg_calculation_time);
    stats += StringFormat("最大计算时间: %.2fms\n", g_performance_stats.max_calculation_time);
    stats += StringFormat("缓存命中率: %.1f%%\n", g_performance_stats.cache_hit_rate);
    stats += StringFormat("内存使用: %dKB\n", g_performance_stats.memory_usage / 1024);
    
    return stats;
}

//+------------------------------------------------------------------+
//| 主指标入口点 - 用于调试和测试                                    |
//+------------------------------------------------------------------+
void DebugMain()
{
    if(!InpEnableDebugLog) return;
    
    LogInfo("=== 调试信息输出 ===", __FUNCTION__);
    
    // 输出指标状态
    Print(GetIndicatorStatusInfo());
    
    // 输出性能统计
    Print(GetPerformanceStats());
    
    // 输出仪表盘状态
    if(InpShowDashboard)
    {
        Print(GetDashboardStatusInfo());
    }
    
    // 输出EMA详细状态
    Print(GetEMADetailedStatus());
    
    LogInfo("=== 调试信息输出完成 ===", __FUNCTION__);
}