//+------------------------------------------------------------------+
//|                                                       config.mqh |
//|                                  Copyright 2025, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, MetaQuotes Ltd."
#property link      "https://www.mql5.com"

#include "constants.mqh"

//+------------------------------------------------------------------+
//| 基础配置参数                                                     |
//+------------------------------------------------------------------+
input group "=== 基础设置 ==="
input bool InpEnableDebugLog = false;                    // 启用调试日志
input int InpAvgDays = 20;                              // 平均波幅计算天数 (5-100)

//+------------------------------------------------------------------+
//| 仪表盘显示配置                                                   |
//+------------------------------------------------------------------+
input group "=== 仪表盘设置 ==="
input bool InpShowDashboard = true;                     // 显示仪表盘
input int InpDashboardX = DEFAULT_DASHBOARD_X;          // 仪表盘X坐标
input int InpDashboardY = DEFAULT_DASHBOARD_Y;          // 仪表盘Y坐标
input int InpDashboardWidth = DEFAULT_DASHBOARD_WIDTH;   // 仪表盘宽度
input int InpDashboardHeight = DEFAULT_DASHBOARD_HEIGHT; // 仪表盘高度
input string InpDashboardFont = DEFAULT_DASHBOARD_FONT;  // 仪表盘字体
input int InpDashboardFontSize = DEFAULT_DASHBOARD_FONT_SIZE; // 字体大小
input color InpDashboardBgColor = C'40,40,40';          // 背景颜色
input color InpDashboardBorderColor = C'100,100,100';   // 边框颜色
input color InpDashboardTextColor = clrWhite;           // 文字颜色

//+------------------------------------------------------------------+
//| 仪表盘内容配置                                                   |
//+------------------------------------------------------------------+
input group "=== 仪表盘内容 ==="
input bool InpShowSessionInfo = true;                   // 显示时段信息
input bool InpShowVolatility = true;                    // 显示波动率分析
input bool InpShowEMAInfo = true;                       // 显示EMA信息
input bool InpShowKeyLevels = true;                     // 显示关键价位
input bool InpShowCountdown = true;                     // 显示倒计时

//+------------------------------------------------------------------+
//| EMA线配置 - EMA1 (短期)                                          |
//+------------------------------------------------------------------+
input group "=== EMA1设置 (短期) ==="
input bool InpEMA1Enable = true;                        // 启用EMA1
input int InpEMA1Period = DEFAULT_EMA1_PERIOD;          // EMA1周期
input color InpEMA1Color = DEFAULT_EMA1_COLOR;          // EMA1颜色
input int InpEMA1Width = 2;                             // EMA1线宽

//+------------------------------------------------------------------+
//| EMA线配置 - EMA2                                                 |
//+------------------------------------------------------------------+
input group "=== EMA2设置 ==="
input bool InpEMA2Enable = true;                        // 启用EMA2
input int InpEMA2Period = DEFAULT_EMA2_PERIOD;          // EMA2周期
input color InpEMA2Color = DEFAULT_EMA2_COLOR;          // EMA2颜色
input int InpEMA2Width = 2;                             // EMA2线宽

//+------------------------------------------------------------------+
//| EMA线配置 - EMA3                                                 |
//+------------------------------------------------------------------+
input group "=== EMA3设置 ==="
input bool InpEMA3Enable = true;                        // 启用EMA3
input int InpEMA3Period = DEFAULT_EMA3_PERIOD;          // EMA3周期
input color InpEMA3Color = DEFAULT_EMA3_COLOR;          // EMA3颜色
input int InpEMA3Width = 2;                             // EMA3线宽

//+------------------------------------------------------------------+
//| EMA线配置 - EMA4                                                 |
//+------------------------------------------------------------------+
input group "=== EMA4设置 ==="
input bool InpEMA4Enable = true;                        // 启用EMA4
input int InpEMA4Period = DEFAULT_EMA4_PERIOD;          // EMA4周期
input color InpEMA4Color = DEFAULT_EMA4_COLOR;          // EMA4颜色
input int InpEMA4Width = 2;                             // EMA4线宽

//+------------------------------------------------------------------+
//| EMA线配置 - EMA5 (长期)                                          |
//+------------------------------------------------------------------+
input group "=== EMA5设置 (长期) ==="
input bool InpEMA5Enable = true;                        // 启用EMA5
input int InpEMA5Period = DEFAULT_EMA5_PERIOD;          // EMA5周期
input color InpEMA5Color = DEFAULT_EMA5_COLOR;          // EMA5颜色
input int InpEMA5Width = 2;                             // EMA5线宽

//+------------------------------------------------------------------+
//| EMA高级配置                                                      |
//+------------------------------------------------------------------+
input group "=== EMA高级设置 ==="
input bool InpUseDynamicColors = false;                 // 使用动态颜色(根据趋势)
input bool InpShowEMATrend = true;                      // 显示EMA趋势信息
input bool InpShowEMAAlignment = true;                  // 显示EMA排列状态

//+------------------------------------------------------------------+
//| 关键价位线配置                                                   |
//+------------------------------------------------------------------+
input group "=== 关键价位线 ==="
input bool InpShowDailyOpen = true;                     // 显示今日开盘价线
input bool InpShowPrevHigh = true;                      // 显示前日最高价线
input bool InpShowPrevLow = true;                       // 显示前日最低价线
input color InpDailyOpenColor = clrYellow;              // 今日开盘价线颜色
input color InpPrevHighColor = clrRed;                  // 前日最高价线颜色
input color InpPrevLowColor = clrLime;                  // 前日最低价线颜色
input ENUM_LINE_STYLE InpKeyLevelStyle = STYLE_DOT;     // 关键价位线样式
input int InpKeyLevelWidth = 1;                         // 关键价位线宽度

//+------------------------------------------------------------------+
//| 时段背景配置                                                     |
//+------------------------------------------------------------------+
input group "=== 时段背景 ==="
input bool InpShowSessionBackground = false;            // 显示时段背景
input int InpSessionBgTransparency = 90;                // 背景透明度 (0-100)

//+------------------------------------------------------------------+
//| 性能优化配置                                                     |
//+------------------------------------------------------------------+
input group "=== 性能优化 ==="
input int InpUpdateInterval = 1;                        // 更新间隔(秒) 1-60
input bool InpEnableSmartUpdate = true;                 // 启用智能更新(仅在价格变化时)
input int InpMaxHistoryBars = 1000;                     // 最大历史K线数量

//+------------------------------------------------------------------+
//| 验证配置参数                                                     |
//+------------------------------------------------------------------+
bool ValidateConfiguration()
{
    // 验证输入参数的有效性
    if(InpAvgDays <= 0 || InpAvgDays > 1000)
    {
        Print("错误：平均天数参数无效");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 应用配置到全局上下文                                             |
//+------------------------------------------------------------------+
void ApplyConfigurationToContext()
{
    // 应用EMA配置
    g_context.ema_data.is_enabled[0] = InpEMA1Enable;
    g_context.ema_data.period[0] = InpEMA1Period;
    g_context.ema_data.ema_colors[0] = InpEMA1Color;
    g_context.ema_data.line_width[0] = InpEMA1Width;
    
    g_context.ema_data.is_enabled[1] = InpEMA2Enable;
    g_context.ema_data.period[1] = InpEMA2Period;
    g_context.ema_data.ema_colors[1] = InpEMA2Color;
    g_context.ema_data.line_width[1] = InpEMA2Width;
    
    g_context.ema_data.is_enabled[2] = InpEMA3Enable;
    g_context.ema_data.period[2] = InpEMA3Period;
    g_context.ema_data.ema_colors[2] = InpEMA3Color;
    g_context.ema_data.line_width[2] = InpEMA3Width;
    
    g_context.ema_data.is_enabled[3] = InpEMA4Enable;
    g_context.ema_data.period[3] = InpEMA4Period;
    g_context.ema_data.ema_colors[3] = InpEMA4Color;
    g_context.ema_data.line_width[3] = InpEMA4Width;
    
    g_context.ema_data.is_enabled[4] = InpEMA5Enable;
    g_context.ema_data.period[4] = InpEMA5Period;
    g_context.ema_data.ema_colors[4] = InpEMA5Color;
    g_context.ema_data.line_width[4] = InpEMA5Width;
    
    Print("配置已应用到全局上下文");
}

//+------------------------------------------------------------------+
//| 获取启用的EMA数量                                                |
//+------------------------------------------------------------------+
int GetEnabledEMACount()
{
    int count = 0;
    if(InpEMA1Enable) count++;
    if(InpEMA2Enable) count++;
    if(InpEMA3Enable) count++;
    if(InpEMA4Enable) count++;
    if(InpEMA5Enable) count++;
    return count;
}

//+------------------------------------------------------------------+
//| 获取配置摘要信息                                                 |
//+------------------------------------------------------------------+
string GetConfigurationSummary()
{
    string summary = "=== 配置摘要 ===\n";
    
    // 基础设置
    summary += StringFormat("调试日志: %s\n", BoolToString(InpEnableDebugLog));
    summary += StringFormat("平均天数: %d天\n", InpAvgDays);
    
    // 仪表盘设置
    summary += StringFormat("仪表盘: %s", BoolToString(InpShowDashboard));
    if(InpShowDashboard)
    {
        summary += StringFormat(" [%dx%d at (%d,%d)]\n", 
                               InpDashboardWidth, InpDashboardHeight, 
                               InpDashboardX, InpDashboardY);
    }
    else
    {
        summary += "\n";
    }
    
    // EMA设置
    int enabled_ema = GetEnabledEMACount();
    summary += StringFormat("启用EMA: %d/5条\n", enabled_ema);
    
    if(InpEMA1Enable) summary += StringFormat("  EMA1: %d周期\n", InpEMA1Period);
    if(InpEMA2Enable) summary += StringFormat("  EMA2: %d周期\n", InpEMA2Period);
    if(InpEMA3Enable) summary += StringFormat("  EMA3: %d周期\n", InpEMA3Period);
    if(InpEMA4Enable) summary += StringFormat("  EMA4: %d周期\n", InpEMA4Period);
    if(InpEMA5Enable) summary += StringFormat("  EMA5: %d周期\n", InpEMA5Period);
    
    // 显示选项
    summary += "显示选项: ";
    string options = "";
    if(InpShowSessionInfo) options += "时段信息 ";
    if(InpShowVolatility) options += "波动率 ";
    if(InpShowEMAInfo) options += "EMA信息 ";
    if(InpShowKeyLevels) options += "关键价位 ";
    if(InpShowCountdown) options += "倒计时 ";
    summary += (options != "") ? options + "\n" : "无\n";
    
    // 关键价位线
    summary += "价位线: ";
    string lines = "";
    if(InpShowDailyOpen) lines += "日开盘 ";
    if(InpShowPrevHigh) lines += "前日高 ";
    if(InpShowPrevLow) lines += "前日低 ";
    summary += (lines != "") ? lines + "\n" : "无\n";
    
    // 性能设置
    summary += StringFormat("更新间隔: %d秒\n", InpUpdateInterval);
    summary += StringFormat("智能更新: %s\n", BoolToString(InpEnableSmartUpdate));
    summary += StringFormat("最大K线: %d根\n", InpMaxHistoryBars);
    
    return summary;
}

//+------------------------------------------------------------------+
//| 检查配置是否发生变化                                             |
//+------------------------------------------------------------------+
bool HasConfigurationChanged()
{
    static bool last_show_dashboard = true;
    static int last_ema1_period = DEFAULT_EMA1_PERIOD;
    static int last_ema2_period = DEFAULT_EMA2_PERIOD;
    static int last_update_interval = 1;
    static bool first_check = true;
    
    if(first_check)
    {
        last_show_dashboard = InpShowDashboard;
        last_ema1_period = InpEMA1Period;
        last_ema2_period = InpEMA2Period;
        last_update_interval = InpUpdateInterval;
        first_check = false;
        return true; // 首次检查认为有变化
    }
    
    bool changed = false;
    
    if(last_show_dashboard != InpShowDashboard)
    {
        Print("配置变化: 仪表盘显示状态");
        changed = true;
    }
    
    if(last_ema1_period != InpEMA1Period)
    {
        Print("配置变化: EMA1周期");
        changed = true;
    }
    
    if(last_ema2_period != InpEMA2Period)
    {
        Print("配置变化: EMA2周期");
        changed = true;
    }
    
    if(last_update_interval != InpUpdateInterval)
    {
        Print("配置变化: 更新间隔");
        changed = true;
    }
    
    // 更新缓存值
    if(changed)
    {
        last_show_dashboard = InpShowDashboard;
        last_ema1_period = InpEMA1Period;
        last_ema2_period = InpEMA2Period;
        last_update_interval = InpUpdateInterval;
    }
    
    return changed;
}

//+------------------------------------------------------------------+
//| 导出配置为字符串（用于保存）                                     |
//+------------------------------------------------------------------+
string ExportConfiguration()
{
    string config = "# 交易时段仪表盘+EMA指标配置导出\n";
    config += "# 导出时间: " + TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES) + "\n\n";
    
    // 基础设置
    config += "[基础设置]\n";
    config += "启用调试日志=" + BoolToString(InpEnableDebugLog) + "\n";
    config += "平均波幅天数=" + IntegerToString(InpAvgDays) + "\n\n";
    
    // 仪表盘设置
    config += "[仪表盘设置]\n";
    config += "显示仪表盘=" + BoolToString(InpShowDashboard) + "\n";
    config += "X坐标=" + IntegerToString(InpDashboardX) + "\n";
    config += "Y坐标=" + IntegerToString(InpDashboardY) + "\n";
    config += "宽度=" + IntegerToString(InpDashboardWidth) + "\n";
    config += "高度=" + IntegerToString(InpDashboardHeight) + "\n";
    config += "字体=" + InpDashboardFont + "\n";
    config += "字体大小=" + IntegerToString(InpDashboardFontSize) + "\n\n";
    
    // EMA设置
    config += "[EMA设置]\n";
    config += "EMA1启用=" + BoolToString(InpEMA1Enable) + "\n";
    config += "EMA1周期=" + IntegerToString(InpEMA1Period) + "\n";
    config += "EMA2启用=" + BoolToString(InpEMA2Enable) + "\n";
    config += "EMA2周期=" + IntegerToString(InpEMA2Period) + "\n";
    config += "EMA3启用=" + BoolToString(InpEMA3Enable) + "\n";
    config += "EMA3周期=" + IntegerToString(InpEMA3Period) + "\n";
    config += "EMA4启用=" + BoolToString(InpEMA4Enable) + "\n";
    config += "EMA4周期=" + IntegerToString(InpEMA4Period) + "\n";
    config += "EMA5启用=" + BoolToString(InpEMA5Enable) + "\n";
    config += "EMA5周期=" + IntegerToString(InpEMA5Period) + "\n\n";
    
    // 显示选项
    config += "[显示选项]\n";
    config += "显示时段信息=" + BoolToString(InpShowSessionInfo) + "\n";
    config += "显示波动率=" + BoolToString(InpShowVolatility) + "\n";
    config += "显示EMA信息=" + BoolToString(InpShowEMAInfo) + "\n";
    config += "显示关键价位=" + BoolToString(InpShowKeyLevels) + "\n";
    config += "显示倒计时=" + BoolToString(InpShowCountdown) + "\n\n";
    
    // 关键价位线
    config += "[关键价位线]\n";
    config += "显示日开盘=" + BoolToString(InpShowDailyOpen) + "\n";
    config += "显示前日高=" + BoolToString(InpShowPrevHigh) + "\n";
    config += "显示前日低=" + BoolToString(InpShowPrevLow) + "\n\n";
    
    // 性能设置
    config += "[性能设置]\n";
    config += "更新间隔=" + IntegerToString(InpUpdateInterval) + "\n";
    config += "智能更新=" + BoolToString(InpEnableSmartUpdate) + "\n";
    config += "最大历史K线=" + IntegerToString(InpMaxHistoryBars) + "\n";
    
    return config;
}

//+------------------------------------------------------------------+
//| 重置配置为默认值                                                 |
//+------------------------------------------------------------------+
void ResetConfigurationToDefaults()
{
    Print("注意: 配置重置功能需要重新加载指标才能生效");
    Print("默认配置:");
    Print("  EMA1: 20周期, 红色");
    Print("  EMA2: 144周期, 蓝色");
    Print("  EMA3: 169周期, 绿色");
    Print("  EMA4: 288周期, 橙色");
    Print("  EMA5: 338周期, 紫色");
    Print("  仪表盘: 显示, 位置(20,30), 大小300x400");
    Print("  更新间隔: 1秒");
    Print("  平均天数: 20天");
}

//+------------------------------------------------------------------+
//| 获取推荐配置（根据不同交易风格）                                 |
//+------------------------------------------------------------------+
string GetRecommendedConfigurations()
{
    string recommendations = "=== 推荐配置 ===\n\n";
    
    recommendations += "【短线交易配置】\n";
    recommendations += "- EMA周期: 5, 13, 21, 55, 89\n";
    recommendations += "- 更新间隔: 1秒\n";
    recommendations += "- 显示所有信息\n";
    recommendations += "- 启用智能更新\n\n";
    
    recommendations += "【中线交易配置】\n";
    recommendations += "- EMA周期: 20, 50, 100, 200, 300\n";
    recommendations += "- 更新间隔: 5秒\n";
    recommendations += "- 重点显示波动率和关键价位\n\n";
    
    recommendations += "【长线交易配置】\n";
    recommendations += "- EMA周期: 50, 100, 200, 400, 600\n";
    recommendations += "- 更新间隔: 10秒\n";
    recommendations += "- 主要显示EMA排列和趋势\n\n";
    
    recommendations += "【外汇交易配置】\n";
    recommendations += "- EMA周期: 20, 144, 169, 288, 338 (默认)\n";
    recommendations += "- 显示时段信息和波动率\n";
    recommendations += "- 启用关键价位线\n\n";
    
    recommendations += "【加密货币配置】\n";
    recommendations += "- EMA周期: 12, 26, 50, 100, 200\n";
    recommendations += "- 更新间隔: 1秒\n";
    recommendations += "- 显示所有信息\n";
    recommendations += "- 关闭时段背景（24/7市场）\n";
    
    return recommendations;
}
