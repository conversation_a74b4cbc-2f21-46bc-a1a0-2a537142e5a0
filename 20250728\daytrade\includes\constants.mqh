//+------------------------------------------------------------------+
//|                                                    constants.mqh |
//|                                  Copyright 2025, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, MetaQuotes Ltd."
#property link      "https://www.mql5.com"

//+------------------------------------------------------------------+
//| 交易时段枚举定义                                                 |
//+------------------------------------------------------------------+
enum ENUM_TRADING_SESSION
{
    SESSION_NONE = 0,      // 非主要时段
    SESSION_ASIA = 1,      // 亚洲时段
    SESSION_LONDON = 2,    // 伦敦时段
    SESSION_OVERLAP = 3,   // 重叠时段(多方角力)
    SESSION_NEWYORK = 4    // 纽约后半段(一方主导)
};

//+------------------------------------------------------------------+
//| EMA趋势枚举定义                                                  |
//+------------------------------------------------------------------+
enum ENUM_EMA_TREND
{
    EMA_TREND_SIDEWAYS = 0,  // 横盘
    EMA_TREND_UP = 1,        // 上升
    EMA_TREND_DOWN = -1      // 下降
};

//+------------------------------------------------------------------+
//| EMA排列枚举定义                                                  |
//+------------------------------------------------------------------+
enum ENUM_EMA_ALIGNMENT
{
    EMA_ALIGNMENT_MIXED = 0,    // 混合排列
    EMA_ALIGNMENT_BULLISH = 1,  // 多头排列
    EMA_ALIGNMENT_BEARISH = -1  // 空头排列
};

//+------------------------------------------------------------------+
//| 系统常量定义                                                     |
//+------------------------------------------------------------------+
#define MAX_EMA_LINES           5      // 最大EMA线数量
#define MAX_DASHBOARD_LINES     50     // 仪表盘最大行数
#define MAX_BARS_TO_CHECK       2000   // 最大检查K线数量
#define MIN_BARS_FOR_CALCULATION 100   // 计算所需最小K线数量

//+------------------------------------------------------------------+
//| 性能优化常量                                                     |
//+------------------------------------------------------------------+
#define MIN_PRICE_CHANGE        0.00001  // 最小价格变化阈值
#define DEBUG_LOG_INTERVAL      50       // 调试日志输出间隔
#define EMA_STATS_UPDATE_INTERVAL 30     // EMA统计更新间隔(秒)
#define DASHBOARD_UPDATE_INTERVAL 1      // 仪表盘更新间隔(秒)

//+------------------------------------------------------------------+
//| EMA参数限制                                                      |
//+------------------------------------------------------------------+
#define MIN_EMA_PERIOD          2        // EMA最小周期
#define MAX_EMA_PERIOD          1000     // EMA最大周期

//+------------------------------------------------------------------+
//| 时间相关常量                                                     |
//+------------------------------------------------------------------+
#define SECONDS_IN_DAY          86400    // 一天的秒数
#define SECONDS_IN_HOUR         3600     // 一小时的秒数
#define SECONDS_IN_MINUTE       60       // 一分钟的秒数

//+------------------------------------------------------------------+
//| 颜色常量定义                                                     |
//+------------------------------------------------------------------+
#define COLOR_ASIA_SESSION      clrBlue      // 亚洲时段颜色
#define COLOR_LONDON_SESSION    clrGreen     // 伦敦时段颜色
#define COLOR_OVERLAP_SESSION   clrOrange    // 重叠时段颜色
#define COLOR_NEWYORK_SESSION   clrRed       // 纽约时段颜色
#define COLOR_NONE_SESSION      clrGray      // 非主要时段颜色

//+------------------------------------------------------------------+
//| 默认EMA配置                                                      |
//+------------------------------------------------------------------+
#define DEFAULT_EMA1_PERIOD     20       // EMA1默认周期
#define DEFAULT_EMA2_PERIOD     144      // EMA2默认周期
#define DEFAULT_EMA3_PERIOD     169      // EMA3默认周期
#define DEFAULT_EMA4_PERIOD     288      // EMA4默认周期
#define DEFAULT_EMA5_PERIOD     338      // EMA5默认周期

#define DEFAULT_EMA1_COLOR      clrRed       // EMA1默认颜色
#define DEFAULT_EMA2_COLOR      clrBlue      // EMA2默认颜色
#define DEFAULT_EMA3_COLOR      clrGreen     // EMA3默认颜色
#define DEFAULT_EMA4_COLOR      clrOrange    // EMA4默认颜色
#define DEFAULT_EMA5_COLOR      clrMagenta   // EMA5默认颜色

//+------------------------------------------------------------------+
//| 仪表盘默认配置                                                   |
//+------------------------------------------------------------------+
#define DEFAULT_DASHBOARD_X         20       // 仪表盘默认X坐标
#define DEFAULT_DASHBOARD_Y         30       // 仪表盘默认Y坐标
#define DEFAULT_DASHBOARD_WIDTH     300      // 仪表盘默认宽度
#define DEFAULT_DASHBOARD_HEIGHT    400      // 仪表盘默认高度
#define DEFAULT_DASHBOARD_FONT_SIZE 9        // 仪表盘默认字体大小
#define DEFAULT_DASHBOARD_FONT      "Consolas" // 仪表盘默认字体

//+------------------------------------------------------------------+
//| 错误处理常量                                                     |
//+------------------------------------------------------------------+
#define LOG_LEVEL_DEBUG         0        // 调试级别
#define LOG_LEVEL_INFO          1        // 信息级别
#define LOG_LEVEL_WARNING       2        // 警告级别
#define LOG_LEVEL_ERROR         3        // 错误级别

//+------------------------------------------------------------------+
//| 性能监控宏定义                                                   |
//+------------------------------------------------------------------+
#define PERFORMANCE_MONITOR() \
    static int call_count = 0; \
    call_count++; \
    if(call_count % 1000 == 1) { \
        Print(StringFormat("函数调用统计: %s 已调用 %d 次", __FUNCTION__, call_count)); \
    }

//+------------------------------------------------------------------+
//| 获取时段颜色的辅助函数                                           |
//+------------------------------------------------------------------+
color GetSessionColor(ENUM_TRADING_SESSION session)
{
    switch(session)
    {
        case SESSION_ASIA:    return COLOR_ASIA_SESSION;
        case SESSION_LONDON:  return COLOR_LONDON_SESSION;
        case SESSION_OVERLAP: return COLOR_OVERLAP_SESSION;
        case SESSION_NEWYORK: return COLOR_NEWYORK_SESSION;
        default:              return COLOR_NONE_SESSION;
    }
}

//+------------------------------------------------------------------+
//| 安全除法函数                                                     |
//+------------------------------------------------------------------+
double SafeDivision(double numerator, double denominator, double default_value, string context = "")
{
    if(MathAbs(denominator) < MIN_PRICE_CHANGE)
    {
        if(context != "")
        {
            LogWarning(StringFormat("除零保护触发: %s, 分子:%f, 分母:%f", context, numerator, denominator), "SafeDivision");
        }
        return default_value;
    }
    return numerator / denominator;
}

//+------------------------------------------------------------------+
//| 字符串格式化辅助函数                                             |
//+------------------------------------------------------------------+
string FormatPrice(double price, int digits = -1)
{
    if(digits < 0) digits = _Digits;
    return DoubleToString(price, digits);
}

//+------------------------------------------------------------------+
//| 时间格式化辅助函数                                               |
//+------------------------------------------------------------------+
string FormatTime(datetime time, bool show_seconds = false)
{
    return TimeToString(time, show_seconds ? TIME_SECONDS : TIME_MINUTES);
}

//+------------------------------------------------------------------+
//| 百分比格式化辅助函数                                             |
//+------------------------------------------------------------------+
string FormatPercentage(double percentage, int decimal_places = 1)
{
    return StringFormat("%.*f%%", decimal_places, percentage);
}

//+------------------------------------------------------------------+
//| 布尔值转字符串                                                   |
//+------------------------------------------------------------------+
string BoolToString(bool value, string true_str = "是", string false_str = "否")
{
    return value ? true_str : false_str;
}

//+------------------------------------------------------------------+
//| 获取当前时间戳（毫秒）                                           |
//+------------------------------------------------------------------+
ulong GetCurrentTimestamp()
{
    return (ulong)TimeCurrent() * 1000 + (ulong)(GetTickCount() % 1000);
}

//+------------------------------------------------------------------+
//| 计算两个时间的差值（秒）                                         |
//+------------------------------------------------------------------+
int GetTimeDifference(datetime time1, datetime time2)
{
    return (int)(time1 - time2);
}

//+------------------------------------------------------------------+
//| 检查是否为有效的价格值                                           |
//+------------------------------------------------------------------+
bool IsValidPrice(double price)
{
    return (price > 0 && price != EMPTY_VALUE && !MathIsInf(price) && !MathIsNaN(price));
}

//+------------------------------------------------------------------+
//| 检查是否为有效的时间值                                           |
//+------------------------------------------------------------------+
bool IsValidTime(datetime time)
{
    return (time > 0 && time != EMPTY_VALUE);
}

//+------------------------------------------------------------------+
//| 限制数值在指定范围内                                             |
//+------------------------------------------------------------------+
double ClampValue(double value, double min_value, double max_value)
{
    if(value < min_value) return min_value;
    if(value > max_value) return max_value;
    return value;
}

//+------------------------------------------------------------------+
//| 计算数组的平均值                                                 |
//+------------------------------------------------------------------+
double CalculateArrayAverage(const double &array[], int start_index = 0, int count = -1)
{
    int array_size = ArraySize(array);
    if(array_size == 0 || start_index >= array_size) return 0;
    
    if(count < 0 || start_index + count > array_size)
        count = array_size - start_index;
    
    double sum = 0;
    int valid_count = 0;
    
    for(int i = start_index; i < start_index + count; i++)
    {
        if(IsValidPrice(array[i]))
        {
            sum += array[i];
            valid_count++;
        }
    }
    
    return (valid_count > 0) ? (sum / valid_count) : 0;
}

//+------------------------------------------------------------------+
//| 版本信息常量                                                     |
//+------------------------------------------------------------------+
#define INDICATOR_VERSION       "1.0.0"
#define INDICATOR_BUILD_DATE    "2025.01.28"
#define INDICATOR_AUTHOR        "CodeBuddy"
#define INDICATOR_DESCRIPTION   "交易时段实时仪表盘 + EMA 5线指标 (模块化重构版)"

//+------------------------------------------------------------------+
//| 获取版本信息字符串                                               |
//+------------------------------------------------------------------+
string GetVersionInfo()
{
    return StringFormat("%s v%s (%s) by %s", 
                       INDICATOR_DESCRIPTION, 
                       INDICATOR_VERSION, 
                       INDICATOR_BUILD_DATE, 
                       INDICATOR_AUTHOR);
}