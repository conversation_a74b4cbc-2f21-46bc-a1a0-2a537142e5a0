//+------------------------------------------------------------------+
//|                                                      globals.mqh |
//|                                  Copyright 2025, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, MetaQuotes Ltd."
#property link      "https://www.mql5.com"

#include "constants.mqh"

//+------------------------------------------------------------------+
//| 交易时段数据结构                                                 |
//+------------------------------------------------------------------+
struct TradingSession
{
    string   name;              // 时段名称
    datetime startTimeGMT;      // GMT开始时间
    datetime endTimeGMT;        // GMT结束时间
    datetime rawEndTimeGMT;     // 自然结束时间
    color    sessionColor;      // 时段颜色
    ENUM_TRADING_SESSION sessionEnum; // 对应枚举值
};

//+------------------------------------------------------------------+
//| 时段统计数据结构                                                 |
//+------------------------------------------------------------------+
struct SessionStats
{
    double asia_avg_range;      // 亚洲时段平均波幅
    double london_avg_range;    // 伦敦时段平均波幅
    double overlap_avg_range;   // 重叠时段平均波幅
    double newyork_avg_range;   // 纽约时段平均波幅
    datetime last_update;       // 最后更新时间
    bool is_valid;              // 数据有效性标志
};

//+------------------------------------------------------------------+
//| EMA数据结构                                                      |
//+------------------------------------------------------------------+
struct EMAData
{
    double buffer[MAX_EMA_LINES][1000];  // EMA缓冲区数组，固定大小
    int handle[MAX_EMA_LINES];           // EMA句柄数组
    int period[MAX_EMA_LINES];           // EMA周期数组
    color ema_colors[MAX_EMA_LINES];     // EMA颜色数组，重命名避免关键字冲突
    int line_width[MAX_EMA_LINES];       // EMA线宽数组
    bool is_enabled[MAX_EMA_LINES];      // EMA启用状态数组
};
//+------------------------------------------------------------------+
//|                                                      globals.mqh |
//|                                  Copyright 2025, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, MetaQuotes Ltd."
#property link      "https://www.mql5.com"

#include "constants.mqh"

//+------------------------------------------------------------------+
//| 交易时段数据结构                                                 |
//+------------------------------------------------------------------+
struct TradingSession
{
    string   name;              // 时段名称
    datetime startTimeGMT;      // GMT开始时间
    datetime endTimeGMT;        // GMT结束时间
    datetime rawEndTimeGMT;     // 自然结束时间
    color    sessionColor;      // 时段颜色
    ENUM_TRADING_SESSION sessionEnum; // 对应枚举值
};

//+------------------------------------------------------------------+
//| 时段统计数据结构                                                 |
//+------------------------------------------------------------------+
struct SessionStats
{
    double asia_avg_range;      // 亚洲时段平均波幅
    double london_avg_range;    // 伦敦时段平均波幅
    double overlap_avg_range;   // 重叠时段平均波幅
    double newyork_avg_range;   // 纽约时段平均波幅
    datetime last_update;       // 最后更新时间
    bool is_valid;              // 数据有效性标志
};

//+------------------------------------------------------------------+
//| EMA数据结构                                                      |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 夏令时缓存结构                                                   |
//+------------------------------------------------------------------+
struct DSTCache
{
    bool london_dst;            // 伦敦夏令时状态
    bool newyork_dst;           // 纽约夏令时状态
    datetime cache_date;        // 缓存日期
    bool is_valid;              // 缓存有效性
};

//+------------------------------------------------------------------+
//| 价格变化检测缓存                                                 |
//+------------------------------------------------------------------+
struct PriceChangeCache
{
    double cached_high;         // 缓存的最高价
    double cached_low;          // 缓存的最低价
    double cached_close;        // 缓存的收盘价
    datetime last_check;        // 最后检查时间
    bool has_change;            // 是否有变化
};

//+------------------------------------------------------------------+
//| 全局上下文结构体 - 统一状态管理                                  |
//+------------------------------------------------------------------+
struct GlobalContext
{
    // 时间管理相关
    DSTCache dst_cache;                          // 夏令时缓存
    TradingSession session_blueprint[4];         // 时段蓝图数组
    datetime last_blueprint_update;              // 蓝图最后更新时间
    
    // 当前状态
    ENUM_TRADING_SESSION current_session;       // 当前交易时段
    string current_session_name;                // 当前时段名称
    
    // 数据计算相关
    PriceChangeCache price_cache;               // 价格变化缓存
    SessionStats session_stats;                 // 时段统计数据
    
    // 波动率分析数据
    double daily_avg_range;                     // 日均波幅
    double today_range;                         // 今日波幅
    double remaining_range;                     // 预估剩余波幅
    double percentage;                          // 完成百分比
    datetime last_daily_calc;                   // 最后日均计算时间
    
    // 当前时段波动率数据
    double current_session_avg_range;           // 当前时段历史均幅
    double current_session_today_range;         // 当前时段今日现幅
    double current_session_remaining_range;     // 当前时段剩余空间
    double current_session_percentage;          // 当前时段完成进度
    
    // EMA相关数据
    EMAData ema_data;                           // EMA数据结构
    ENUM_EMA_ALIGNMENT ema_alignment;           // EMA排列状态
    ENUM_EMA_TREND ema_trends[MAX_EMA_LINES];   // 各EMA趋势状态
    double ema_support;                         // EMA支撑位
    double ema_resistance;                      // EMA阻力位
    datetime ema_stats_update;                  // EMA统计更新时间
    
    // 关键价位数据
    double daily_open_price;                    // 今日开盘价
    double prev_high_price;                     // 前日最高价
    double prev_low_price;                      // 前日最低价
    datetime key_levels_update;                 // 关键价位更新时间
    
    // 更新时间戳
    datetime last_data_update;                  // 最后数据更新时间
    datetime last_backup_update;                // 最后备份更新时间
    datetime last_ui_update;                    // 最后UI更新时间
};

//+------------------------------------------------------------------+
//| 全局上下文实例                                                   |
//+------------------------------------------------------------------+
GlobalContext g_context;

//+------------------------------------------------------------------+
//| 初始化全局上下文                                                 |
//+------------------------------------------------------------------+
void InitializeGlobalContext()
{
    // 初始化时间管理
    g_context.dst_cache.is_valid = false;
    g_context.dst_cache.cache_date = 0;
    g_context.dst_cache.london_dst = false;
    g_context.dst_cache.newyork_dst = false;
    g_context.last_blueprint_update = 0;
    
    // 初始化当前状态
    g_context.current_session = SESSION_NONE;
    g_context.current_session_name = "未知";
    
    // 初始化价格缓存
    g_context.price_cache.cached_high = 0;
    g_context.price_cache.cached_low = 0;
    g_context.price_cache.cached_close = 0;
    g_context.price_cache.last_check = 0;
    g_context.price_cache.has_change = false;
    
    // 初始化时段统计
    g_context.session_stats.asia_avg_range = 0;
    g_context.session_stats.london_avg_range = 0;
    g_context.session_stats.overlap_avg_range = 0;
    g_context.session_stats.newyork_avg_range = 0;
    g_context.session_stats.last_update = 0;
    g_context.session_stats.is_valid = false;
    
    // 初始化波动率数据
    g_context.daily_avg_range = 0;
    g_context.today_range = 0;
    g_context.remaining_range = 0;
    g_context.percentage = 0;
    g_context.last_daily_calc = 0;
    
    // 初始化当前时段波动率数据
    g_context.current_session_avg_range = 0;
    g_context.current_session_today_range = 0;
    g_context.current_session_remaining_range = 0;
    g_context.current_session_percentage = 0;
    
    // 初始化EMA数据
    for(int i = 0; i < MAX_EMA_LINES; i++)
    {
        g_context.ema_data.handle[i] = INVALID_HANDLE;
        g_context.ema_data.period[i] = 0;
        g_context.ema_data.color[i] = clrWhite;
        g_context.ema_data.line_width[i] = 1;
        g_context.ema_data.is_enabled[i] = false;
        g_context.ema_trends[i] = EMA_TREND_SIDEWAYS;
    }
    
    g_context.ema_alignment = EMA_ALIGNMENT_MIXED;
    g_context.ema_support = EMPTY_VALUE;
    g_context.ema_resistance = EMPTY_VALUE;
    g_context.ema_stats_update = 0;
    
    // 初始化关键价位
    g_context.daily_open_price = 0;
    g_context.prev_high_price = 0;
    g_context.prev_low_price = 0;
    g_context.key_levels_update = 0;
    
    // 初始化时间戳
    g_context.last_data_update = 0;
    g_context.last_backup_update = 0;
    g_context.last_ui_update = 0;
    
    // 初始化时段蓝图数组
    for(int i = 0; i < 4; i++)
    {
        g_context.session_blueprint[i].name = "";
        g_context.session_blueprint[i].startTimeGMT = 0;
        g_context.session_blueprint[i].endTimeGMT = 0;
        g_context.session_blueprint[i].rawEndTimeGMT = 0;
        g_context.session_blueprint[i].sessionColor = clrGray;
        g_context.session_blueprint[i].sessionEnum = SESSION_NONE;
    }
}

//+------------------------------------------------------------------+
//| 重置全局上下文（用于重新初始化）                                 |
//+------------------------------------------------------------------+
void ResetGlobalContext()
{
    // 清理EMA句柄
    for(int i = 0; i < MAX_EMA_LINES; i++)
    {
        if(g_context.ema_data.handle[i] != INVALID_HANDLE)
        {
            IndicatorRelease(g_context.ema_data.handle[i]);
            g_context.ema_data.handle[i] = INVALID_HANDLE;
        }
    }
    
    // 重新初始化
    InitializeGlobalContext();
}

//+------------------------------------------------------------------+
//| 获取全局上下文状态信息                                           |
//+------------------------------------------------------------------+
string GetGlobalContextStatus()
{
    string status = "=== 全局上下文状态 ===\n";
    
    // 当前时段信息
    status += StringFormat("当前时段: %s\n", g_context.current_session_name);
    status += StringFormat("夏令时缓存: %s\n", g_context.dst_cache.is_valid ? "有效" : "无效");
    
    // 数据状态
    status += StringFormat("价格缓存: %s\n", g_context.price_cache.has_change ? "有变化" : "无变化");
    status += StringFormat("时段统计: %s\n", g_context.session_stats.is_valid ? "有效" : "无效");
    
    // EMA状态
    int enabled_ema_count = 0;
    for(int i = 0; i < MAX_EMA_LINES; i++)
    {
        if(g_context.ema_data.is_enabled[i]) enabled_ema_count++;
    }
    status += StringFormat("启用EMA数量: %d/%d\n", enabled_ema_count, MAX_EMA_LINES);
    
    // 更新时间
    status += StringFormat("最后数据更新: %s\n", TimeToString(g_context.last_data_update, TIME_MINUTES));
    status += StringFormat("最后UI更新: %s\n", TimeToString(g_context.last_ui_update, TIME_MINUTES));
    
    return status;
}

//+------------------------------------------------------------------+
//| 检查全局上下文完整性                                             |
//+------------------------------------------------------------------+
bool ValidateGlobalContext()
{
    bool is_valid = true;
    
    // 检查EMA数据完整性
    for(int i = 0; i < MAX_EMA_LINES; i++)
    {
        if(g_context.ema_data.is_enabled[i])
        {
            if(g_context.ema_data.handle[i] == INVALID_HANDLE)
            {
                Print(StringFormat("错误: EMA%d句柄无效", i+1));
                is_valid = false;
            }
            
            if(g_context.ema_data.period[i] <= 0)
            {
                Print(StringFormat("错误: EMA%d周期无效: %d", i+1, g_context.ema_data.period[i]));
                is_valid = false;
            }
        }
    }
    
    // 检查时间戳合理性
    datetime current_time = TimeCurrent();
    if(g_context.last_data_update > current_time + 3600) // 不能超过当前时间1小时
    {
        Print("警告: 数据更新时间戳异常");
        is_valid = false;
    }
    
    return is_valid;
}

//+------------------------------------------------------------------+
//| 更新全局上下文时间戳                                             |
//+------------------------------------------------------------------+
void UpdateGlobalContextTimestamps()
{
    datetime current_time = TimeCurrent();
    g_context.last_data_update = current_time;
}

//+------------------------------------------------------------------+
//| 获取内存使用情况估算                                             |
//+------------------------------------------------------------------+
string GetMemoryUsageEstimate()
{
    int total_bytes = 0;
    
    // 基础结构体大小估算
    total_bytes += sizeof(GlobalContext);
    
    // EMA缓冲区大小估算（假设每个缓冲区1000个数据点）
    int enabled_ema_count = 0;
    for(int i = 0; i < MAX_EMA_LINES; i++)
    {
        if(g_context.ema_data.is_enabled[i])
        {
            enabled_ema_count++;
        }
    }
    total_bytes += enabled_ema_count * 1000 * sizeof(double);
    
    // 转换为KB
    double total_kb = total_bytes / 1024.0;
    
    return StringFormat("估算内存使用: %.2f KB (%d bytes)", total_kb, total_bytes);
}

//+------------------------------------------------------------------+
//| 导出全局上下文配置（用于调试）                                   |
//+------------------------------------------------------------------+
string ExportGlobalContextConfig()
{
    string config = "=== 全局上下文配置导出 ===\n";
    
    // EMA配置
    config += "EMA配置:\n";
    for(int i = 0; i < MAX_EMA_LINES; i++)
    {
        if(g_context.ema_data.is_enabled[i])
        {
            config += StringFormat("  EMA%d: 周期=%d, 颜色=%d, 线宽=%d\n", 
                                  i+1, 
                                  g_context.ema_data.period[i], 
                                  g_context.ema_data.color[i], 
                                  g_context.ema_data.line_width[i]);
        }
        else
        {
            config += StringFormat("  EMA%d: 禁用\n", i+1);
        }
    }
    
    // 时段统计配置
    config += "\n时段统计:\n";
    config += StringFormat("  亚洲时段均幅: %f\n", g_context.session_stats.asia_avg_range);
    config += StringFormat("  伦敦时段均幅: %f\n", g_context.session_stats.london_avg_range);
    config += StringFormat("  重叠时段均幅: %f\n", g_context.session_stats.overlap_avg_range);
    config += StringFormat("  纽约时段均幅: %f\n", g_context.session_stats.newyork_avg_range);
    
    // 当前状态
    config += "\n当前状态:\n";
    config += StringFormat("  当前时段: %s\n", g_context.current_session_name);
    config += StringFormat("  日均波幅: %f\n", g_context.daily_avg_range);
    config += StringFormat("  今日波幅: %f\n", g_context.today_range);
    config += StringFormat("  完成百分比: %.1f%%\n", g_context.percentage);
    
    return config;
}

//+------------------------------------------------------------------+
//| 清理全局上下文                                                   |
//+------------------------------------------------------------------+
void CleanupGlobalContext()
{
    // 重置所有全局变量到初始状态
    g_context.current_session = SESSION_NONE;
    g_context.current_session_name = "";
    g_context.daily_avg_range = 0;
    g_context.today_range = 0;
    g_context.remaining_range = 0;
    g_context.percentage = 0;
}
