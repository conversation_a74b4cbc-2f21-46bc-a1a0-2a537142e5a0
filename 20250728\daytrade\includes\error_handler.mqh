//+------------------------------------------------------------------+
//|                                               error_handler.mqh |
//|                                  Copyright 2025, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, MetaQuotes Ltd."
#property link      "https://www.mql5.com"

#include "constants.mqh"
#include "config.mqh"

//+------------------------------------------------------------------+
//| 错误级别枚举                                                     |
//+------------------------------------------------------------------+
enum ENUM_LOG_LEVEL
{
    LOG_LEVEL_DEBUG = 0,    // 调试信息
    LOG_LEVEL_INFO = 1,     // 一般信息
    LOG_LEVEL_WARNING = 2,  // 警告信息
    LOG_LEVEL_ERROR = 3,    // 错误信息
    LOG_LEVEL_CRITICAL = 4  // 严重错误
};

//+------------------------------------------------------------------+
//| 性能监控结构体                                                   |
//+------------------------------------------------------------------+
struct PerformanceMonitor
{
    string function_name;   // 函数名称
    ulong start_time;      // 开始时间
    ulong end_time;        // 结束时间
    ulong duration;        // 执行时长(微秒)
};

//+------------------------------------------------------------------+
//| 全局性能统计                                                     |
//+------------------------------------------------------------------+
static int g_total_function_calls = 0;
static ulong g_total_execution_time = 0;
static string g_slowest_function = "";
static ulong g_slowest_duration = 0;

//+------------------------------------------------------------------+
//| 性能监控宏定义                                                   |
//+------------------------------------------------------------------+
#define PERFORMANCE_MONITOR() \
    static int perf_call_count = 0; \
    perf_call_count++; \
    if(perf_call_count % 1000 == 1) { \
        Print(StringFormat("性能监控: %s 已调用 %d 次", __FUNCTION__, perf_call_count)); \
    }

//+------------------------------------------------------------------+
//| 获取当前时间戳（微秒）                                           |
//+------------------------------------------------------------------+
ulong GetCurrentTimestamp()
{
    return (ulong)(GetTickCount64() * 1000); // 转换为微秒
}

//+------------------------------------------------------------------+
//| 记录性能数据                                                     |
//+------------------------------------------------------------------+
void RecordPerformanceData(const PerformanceMonitor &monitor)
{
    g_total_function_calls++;
    g_total_execution_time += monitor.duration;
    
    // 记录最慢的函数
    if(monitor.duration > g_slowest_duration)
    {
        g_slowest_duration = monitor.duration;
        g_slowest_function = monitor.function_name;
    }
    
    // 如果执行时间超过阈值，记录警告
    if(monitor.duration > PERFORMANCE_WARNING_THRESHOLD)
    {
        LogWarning(StringFormat("函数执行缓慢: %s 耗时 %d 微秒", 
                               monitor.function_name, monitor.duration), "PerformanceMonitor");
    }
}

//+------------------------------------------------------------------+
//| 日志输出函数 - 调试级别                                          |
//+------------------------------------------------------------------+
void LogDebug(string message, string function_name = "")
{
    if(!InpEnableDebugLog) return;
    
    string log_message = FormatLogMessage(LOG_LEVEL_DEBUG, message, function_name);
    Print(log_message);
}

//+------------------------------------------------------------------+
//| 日志输出函数 - 信息级别                                          |
//+------------------------------------------------------------------+
void LogInfo(string message, string function_name = "")
{
    string log_message = FormatLogMessage(LOG_LEVEL_INFO, message, function_name);
    Print(log_message);
}

//+------------------------------------------------------------------+
//| 日志输出函数 - 警告级别                                          |
//+------------------------------------------------------------------+
void LogWarning(string message, string function_name = "")
{
    string log_message = FormatLogMessage(LOG_LEVEL_WARNING, message, function_name);
    Print(log_message);
}

//+------------------------------------------------------------------+
//| 日志输出函数 - 错误级别                                          |
//+------------------------------------------------------------------+
void LogError(string message, string function_name = "")
{
    string log_message = FormatLogMessage(LOG_LEVEL_ERROR, message, function_name);
    Print(log_message);
    
    // 错误级别的日志同时输出到专家日志
    Comment("错误: " + message);
}

//+------------------------------------------------------------------+
//| 日志输出函数 - 严重错误级别                                      |
//+------------------------------------------------------------------+
void LogCritical(string message, string function_name = "")
{
    string log_message = FormatLogMessage(LOG_LEVEL_CRITICAL, message, function_name);
    Print(log_message);
    
    // 严重错误同时弹出警告框
    Alert("严重错误: " + message);
    Comment("严重错误: " + message);
}

//+------------------------------------------------------------------+
//| 格式化日志消息                                                   |
//+------------------------------------------------------------------+
string FormatLogMessage(ENUM_LOG_LEVEL level, string message, string function_name)
{
    string level_text = "";
    switch(level)
    {
        case LOG_LEVEL_DEBUG:    level_text = "[调试]"; break;
        case LOG_LEVEL_INFO:     level_text = "[信息]"; break;
        case LOG_LEVEL_WARNING:  level_text = "[警告]"; break;
        case LOG_LEVEL_ERROR:    level_text = "[错误]"; break;
        case LOG_LEVEL_CRITICAL: level_text = "[严重]"; break;
    }
    
    string timestamp = TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES|TIME_SECONDS);
    string func_info = (function_name != "") ? StringFormat("[%s] ", function_name) : "";
    
    return StringFormat("%s %s %s%s", timestamp, level_text, func_info, message);
}

//+------------------------------------------------------------------+
//| 数据验证函数 - 价格验证                                          |
//+------------------------------------------------------------------+
bool ValidatePrice(double price, string price_name = "", string function_name = "")
{
    if(price <= 0 || price == EMPTY_VALUE || price != price) // 检查NaN
    {
        if(price_name != "" && function_name != "")
        {
            LogError(StringFormat("价格数据无效: %s = %f", price_name, price), function_name);
        }
        return false;
    }
    return true;
}

//+------------------------------------------------------------------+
//| 数据验证函数 - 时间验证                                          |
//+------------------------------------------------------------------+
bool ValidateDateTime(datetime dt, string dt_name = "", string function_name = "")
{
    if(dt <= 0 || dt == EMPTY_VALUE)
    {
        if(dt_name != "" && function_name != "")
        {
            LogError(StringFormat("时间数据无效: %s = %s", dt_name, TimeToString(dt)), function_name);
        }
        return false;
    }
    return true;
}

//+------------------------------------------------------------------+
//| 数据验证函数 - 数组大小验证                                      |
//+------------------------------------------------------------------+
bool ValidateArraySize(const double &array[], int min_size, string array_name = "", string function_name = "")
{
    int size = ArraySize(array);
    if(size < min_size)
    {
        if(array_name != "" && function_name != "")
        {
            LogError(StringFormat("数组大小不足: %s 大小=%d, 需要>=%d", array_name, size, min_size), function_name);
        }
        return false;
    }
    return true;
}

//+------------------------------------------------------------------+
//| 数据验证函数 - 除法安全检查                                      |
//+------------------------------------------------------------------+
bool ValidateDivision(double divisor, string divisor_name = "", string function_name = "")
{
    if(MathAbs(divisor) < MIN_DIVISION_THRESHOLD)
    {
        if(divisor_name != "" && function_name != "")
        {
            LogWarning(StringFormat("除数过小，可能导致除零错误: %s = %f", divisor_name, divisor), function_name);
        }
        return false;
    }
    return true;
}

//+------------------------------------------------------------------+
//| 安全除法函数                                                     |
//+------------------------------------------------------------------+
double SafeDivision(double dividend, double divisor, double default_value = 0.0, string function_name = "")
{
    if(!ValidateDivision(divisor, "divisor", function_name))
    {
        LogWarning(StringFormat("使用默认值 %f 替代除法结果", default_value), function_name);
        return default_value;
    }
    return dividend / divisor;
}

//+------------------------------------------------------------------+
//| 错误恢复函数 - 重置价格缓存                                      |
//+------------------------------------------------------------------+
void ResetPriceCache()
{
    LogInfo("重置价格缓存", __FUNCTION__);
    
    // 这里需要访问全局上下文，但为了避免循环依赖，使用外部函数
    // g_context.price_cache.cached_high = 0;
    // g_context.price_cache.cached_low = 0;
    // g_context.price_cache.cached_close = 0;
    // g_context.price_cache.has_change = false;
}

//+------------------------------------------------------------------+
//| 错误恢复函数 - 重置时段统计                                      |
//+------------------------------------------------------------------+
void ResetSessionStats()
{
    LogInfo("重置时段统计数据", __FUNCTION__);
    
    // 这里需要访问全局上下文
    // g_context.session_stats.is_valid = false;
    // g_context.session_stats.last_update = 0;
}

//+------------------------------------------------------------------+
//| 获取最后一个错误信息                                             |
//+------------------------------------------------------------------+
string GetLastErrorDescription()
{
    int error_code = GetLastError();
    if(error_code == 0) return "无错误";
    
    string error_desc = "";
    switch(error_code)
    {
        case ERR_NO_ERROR:                    error_desc = "无错误"; break;
        case ERR_NO_RESULT:                   error_desc = "无结果"; break;
        case ERR_COMMON_ERROR:                error_desc = "一般错误"; break;
        case ERR_INVALID_TRADE_PARAMETERS:    error_desc = "无效的交易参数"; break;
        case ERR_SERVER_BUSY:                 error_desc = "服务器忙"; break;
        case ERR_OLD_VERSION:                 error_desc = "版本过旧"; break;
        case ERR_NO_CONNECTION:               error_desc = "无连接"; break;
        case ERR_NOT_ENOUGH_RIGHTS:           error_desc = "权限不足"; break;
        case ERR_TOO_FREQUENT_REQUESTS:       error_desc = "请求过于频繁"; break;
        case ERR_MALFUNCTIONAL_TRADE:         error_desc = "交易功能故障"; break;
        case ERR_ACCOUNT_DISABLED:            error_desc = "账户被禁用"; break;
        case ERR_INVALID_ACCOUNT:             error_desc = "无效账户"; break;
        case ERR_TRADE_TIMEOUT:               error_desc = "交易超时"; break;
        case ERR_INVALID_PRICE:               error_desc = "无效价格"; break;
        case ERR_INVALID_STOPS:               error_desc = "无效止损"; break;
        case ERR_INVALID_TRADE_VOLUME:        error_desc = "无效交易量"; break;
        case ERR_MARKET_CLOSED:               error_desc = "市场关闭"; break;
        case ERR_TRADE_DISABLED:              error_desc = "交易被禁用"; break;
        case ERR_NOT_ENOUGH_MONEY:            error_desc = "资金不足"; break;
        case ERR_PRICE_CHANGED:               error_desc = "价格变化"; break;
        case ERR_OFF_QUOTES:                  error_desc = "无报价"; break;
        case ERR_BROKER_BUSY:                 error_desc = "经纪商忙"; break;
        case ERR_REQUOTE:                     error_desc = "重新报价"; break;
        case ERR_ORDER_LOCKED:                error_desc = "订单锁定"; break;
        case ERR_LONG_POSITIONS_ONLY_ALLOWED: error_desc = "仅允许多头"; break;
        case ERR_TOO_MANY_REQUESTS:           error_desc = "请求过多"; break;
        default:                              error_desc = StringFormat("未知错误 (%d)", error_code); break;
    }
    
    return StringFormat("[%d] %s", error_code, error_desc);
}

//+------------------------------------------------------------------+
//| 检查并报告系统错误                                               |
//+------------------------------------------------------------------+
bool CheckAndReportError(string operation_name, string function_name = "")
{
    int error_code = GetLastError();
    if(error_code != 0)
    {
        string error_desc = GetLastErrorDescription();
        LogError(StringFormat("%s 失败: %s", operation_name, error_desc), function_name);
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| 内存使用情况检查                                                 |
//+------------------------------------------------------------------+
void CheckMemoryUsage(string checkpoint_name = "")
{
    // MQL5没有直接的内存使用API，这里提供一个框架
    static int check_count = 0;
    check_count++;
    
    if(check_count % 100 == 0) // 每100次检查一次
    {
        LogDebug(StringFormat("内存检查点 #%d: %s", check_count, checkpoint_name), __FUNCTION__);
        
        // 可以在这里添加自定义的内存使用估算逻辑
        // 例如：统计全局数组的大小、对象数量等
    }
}

//+------------------------------------------------------------------+
//| 获取性能统计报告                                                 |
//+------------------------------------------------------------------+
string GetPerformanceReport()
{
    string report = "=== 性能统计报告 ===\n";
    report += StringFormat("总函数调用次数: %d\n", g_total_function_calls);
    report += StringFormat("总执行时间: %d 微秒\n", g_total_execution_time);
    
    if(g_total_function_calls > 0)
    {
        ulong avg_time = g_total_execution_time / g_total_function_calls;
        report += StringFormat("平均执行时间: %d 微秒\n", avg_time);
    }
    
    if(g_slowest_function != "")
    {
        report += StringFormat("最慢函数: %s (%d 微秒)\n", g_slowest_function, g_slowest_duration);
    }
    
    return report;
}

//+------------------------------------------------------------------+
//| 重置性能统计                                                     |
//+------------------------------------------------------------------+
void ResetPerformanceStats()
{
    g_total_function_calls = 0;
    g_total_execution_time = 0;
    g_slowest_function = "";
    g_slowest_duration = 0;
    
    LogInfo("性能统计已重置", __FUNCTION__);
}

//+------------------------------------------------------------------+
//| 断言函数                                                         |
//+------------------------------------------------------------------+
bool Assert(bool condition, string message, string function_name = "")
{
    if(!condition)
    {
        LogCritical(StringFormat("断言失败: %s", message), function_name);
        return false;
    }
    return true;
}

//+------------------------------------------------------------------+
//| 范围检查函数                                                     |
//+------------------------------------------------------------------+
bool ValidateRange(double value, double min_value, double max_value, string value_name = "", string function_name = "")
{
    if(value < min_value || value > max_value)
    {
        if(value_name != "" && function_name != "")
        {
            LogError(StringFormat("数值超出范围: %s = %f, 范围[%f, %f]", 
                                 value_name, value, min_value, max_value), function_name);
        }
        return false;
    }
    return true;
}

//+------------------------------------------------------------------+
//| 字符串验证函数                                                   |
//+------------------------------------------------------------------+
bool ValidateString(string str, string str_name = "", string function_name = "")
{
    if(str == "" || str == NULL)
    {
        if(str_name != "" && function_name != "")
        {
            LogError(StringFormat("字符串为空: %s", str_name), function_name);
        }
        return false;
    }
    return true;
}

//+------------------------------------------------------------------+
//| 获取版本信息                                                     |
//+------------------------------------------------------------------+
string GetVersionInfo()
{
    return StringFormat("交易时段仪表盘+EMA指标 v%s - 模块化重构版", INDICATOR_VERSION);
}

//+------------------------------------------------------------------+
//| 布尔值转字符串                                                   |
//+------------------------------------------------------------------+
string BoolToString(bool value)
{
    return value ? "是" : "否";
}

//+------------------------------------------------------------------+
//| 清理错误处理模块                                                 |
//+------------------------------------------------------------------+
void CleanupErrorHandler()
{
    // 输出最终的性能报告
    if(g_total_function_calls > 0)
    {
        LogInfo("最终性能报告:", __FUNCTION__);
        Print(GetPerformanceReport());
    }
    
    // 重置统计数据
    ResetPerformanceStats();
    
    LogInfo("错误处理模块清理完成", __FUNCTION__);
}

//+------------------------------------------------------------------+
//| 初始化错误处理模块                                               |
//+------------------------------------------------------------------+
void InitializeErrorHandler()
{
    ResetPerformanceStats();
    LogInfo("错误处理模块初始化完成", __FUNCTION__);
}

//+------------------------------------------------------------------+
//| 作用域退出处理器（模拟C++的RAII）                                |
//+------------------------------------------------------------------+
template<typename T>
void OnScopeExit(T func)
{
    // 这是一个概念性的实现，MQL5可能不支持完整的RAII
    // 在实际使用中，需要手动在函数结束前调用性能记录
}

//+------------------------------------------------------------------+
//| 错误恢复策略枚举                                                 |
//+------------------------------------------------------------------+
enum ENUM_ERROR_RECOVERY_STRATEGY
{
    RECOVERY_IGNORE,        // 忽略错误
    RECOVERY_RETRY,         // 重试操作
    RECOVERY_RESET,         // 重置状态
    RECOVERY_FALLBACK,      // 使用备用方案
    RECOVERY_ABORT          // 中止操作
};

//+------------------------------------------------------------------+
//| 执行错误恢复策略                                                 |
//+------------------------------------------------------------------+
bool ExecuteErrorRecovery(ENUM_ERROR_RECOVERY_STRATEGY strategy, string operation_name, string function_name = "")
{
    switch(strategy)
    {
        case RECOVERY_IGNORE:
            LogWarning(StringFormat("忽略错误: %s", operation_name), function_name);
            return true;
            
        case RECOVERY_RETRY:
            LogInfo(StringFormat("准备重试: %s", operation_name), function_name);
            return false; // 返回false表示需要重试
            
        case RECOVERY_RESET:
            LogInfo(StringFormat("重置状态: %s", operation_name), function_name);
            ResetPriceCache();
            ResetSessionStats();
            return true;
            
        case RECOVERY_FALLBACK:
            LogInfo(StringFormat("使用备用方案: %s", operation_name), function_name);
            return true;
            
        case RECOVERY_ABORT:
            LogError(StringFormat("中止操作: %s", operation_name), function_name);
            return false;
            
        default:
            LogError("未知的错误恢复策略", function_name);
            return false;
    }
}