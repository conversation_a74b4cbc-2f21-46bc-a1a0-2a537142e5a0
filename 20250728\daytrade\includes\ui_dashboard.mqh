//+------------------------------------------------------------------+
//|                                                 ui_dashboard.mqh |
//|                                  Copyright 2025, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, MetaQuotes Ltd."
#property link      "https://www.mql5.com"

#include "constants.mqh"
#include "globals.mqh"
#include "config.mqh"
#include "error_handler.mqh"
#include "time_functions.mqh"
#include "data_calculator.mqh"
#include "ema_logic.mqh"

//+------------------------------------------------------------------+
//| 仪表盘对象结构体                                                 |
//+------------------------------------------------------------------+
struct DashboardObjects
{
    long chart_id;                    // 图表ID
    string background_name;           // 背景对象名称
    string title_name;                // 标题对象名称
    string session_name;              // 时段名称对象
    string range_info_name;           // 波幅信息对象
    string percentage_name;           // 百分比对象
    string ema_info_name;             // EMA信息对象
    string time_info_name;            // 时间信息对象
    string status_name;               // 状态信息对象
    datetime last_update;             // 最后更新时间
    bool is_created;                  // 是否已创建
};

// 全局仪表盘对象
DashboardObjects g_dashboard;

//+------------------------------------------------------------------+
//| 初始化仪表盘                                                     |
//+------------------------------------------------------------------+
bool InitializeDashboard()
{
    PERFORMANCE_MONITOR();
    
    LogInfo("开始初始化交易时段仪表盘", __FUNCTION__);
    
    // 初始化仪表盘对象结构
    g_dashboard.chart_id = ChartID();
    g_dashboard.background_name = DASHBOARD_PREFIX + "Background";
    g_dashboard.title_name = DASHBOARD_PREFIX + "Title";
    g_dashboard.session_name = DASHBOARD_PREFIX + "Session";
    g_dashboard.range_info_name = DASHBOARD_PREFIX + "RangeInfo";
    g_dashboard.percentage_name = DASHBOARD_PREFIX + "Percentage";
    g_dashboard.ema_info_name = DASHBOARD_PREFIX + "EMAInfo";
    g_dashboard.time_info_name = DASHBOARD_PREFIX + "TimeInfo";
    g_dashboard.status_name = DASHBOARD_PREFIX + "Status";
    g_dashboard.last_update = 0;
    g_dashboard.is_created = false;
    
    // 清理可能存在的旧对象
    CleanupDashboard();
    
    // 创建仪表盘对象
    if(!CreateDashboardObjects())
    {
        LogError("创建仪表盘对象失败", __FUNCTION__);
        return false;
    }
    
    // 初始化显示内容
    if(!UpdateDashboardContent())
    {
        LogError("初始化仪表盘内容失败", __FUNCTION__);
        return false;
    }
    
    g_dashboard.is_created = true;
    g_dashboard.last_update = TimeCurrent();
    
    LogInfo("交易时段仪表盘初始化完成", __FUNCTION__);
    return true;
}

//+------------------------------------------------------------------+
//| 创建仪表盘对象                                                   |
//+------------------------------------------------------------------+
bool CreateDashboardObjects()
{
    PERFORMANCE_MONITOR();
    
    LogDebug("开始创建仪表盘对象", __FUNCTION__);
    
    // 1. 创建背景矩形
    if(!CreateBackgroundRectangle())
    {
        LogError("创建背景矩形失败", __FUNCTION__);
        return false;
    }
    
    // 2. 创建标题文本
    if(!CreateTitleLabel())
    {
        LogError("创建标题文本失败", __FUNCTION__);
        return false;
    }
    
    // 3. 创建时段信息标签
    if(!CreateSessionLabel())
    {
        LogError("创建时段信息标签失败", __FUNCTION__);
        return false;
    }
    
    // 4. 创建波幅信息标签
    if(!CreateRangeInfoLabel())
    {
        LogError("创建波幅信息标签失败", __FUNCTION__);
        return false;
    }
    
    // 5. 创建百分比标签
    if(!CreatePercentageLabel())
    {
        LogError("创建百分比标签失败", __FUNCTION__);
        return false;
    }
    
    // 6. 创建EMA信息标签
    if(!CreateEMAInfoLabel())
    {
        LogError("创建EMA信息标签失败", __FUNCTION__);
        return false;
    }
    
    // 7. 创建时间信息标签
    if(!CreateTimeInfoLabel())
    {
        LogError("创建时间信息标签失败", __FUNCTION__);
        return false;
    }
    
    // 8. 创建状态信息标签
    if(!CreateStatusLabel())
    {
        LogError("创建状态信息标签失败", __FUNCTION__);
        return false;
    }
    
    LogDebug("仪表盘对象创建完成", __FUNCTION__);
    return true;
}

//+------------------------------------------------------------------+
//| 创建背景矩形                                                     |
//+------------------------------------------------------------------+
bool CreateBackgroundRectangle()
{
    if(!ObjectCreate(g_dashboard.chart_id, g_dashboard.background_name, OBJ_RECTANGLE_LABEL, 0, 0, 0))
    {
        LogError("创建背景矩形对象失败", __FUNCTION__);
        return false;
    }
    
    // 设置背景属性
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.background_name, OBJPROP_CORNER, InpDashboardCorner);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.background_name, OBJPROP_XDISTANCE, InpDashboardX);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.background_name, OBJPROP_YDISTANCE, InpDashboardY);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.background_name, OBJPROP_XSIZE, DASHBOARD_WIDTH);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.background_name, OBJPROP_YSIZE, DASHBOARD_HEIGHT);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.background_name, OBJPROP_BGCOLOR, InpDashboardBgColor);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.background_name, OBJPROP_BORDER_COLOR, InpDashboardBorderColor);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.background_name, OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.background_name, OBJPROP_WIDTH, 1);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.background_name, OBJPROP_BACK, false);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.background_name, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.background_name, OBJPROP_SELECTED, false);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.background_name, OBJPROP_HIDDEN, true);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.background_name, OBJPROP_ZORDER, 0);
    
    return true;
}

//+------------------------------------------------------------------+
//| 创建标题标签                                                     |
//+------------------------------------------------------------------+
bool CreateTitleLabel()
{
    if(!ObjectCreate(g_dashboard.chart_id, g_dashboard.title_name, OBJ_LABEL, 0, 0, 0))
    {
        LogError("创建标题标签对象失败", __FUNCTION__);
        return false;
    }
    
    // 设置标题属性
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.title_name, OBJPROP_CORNER, InpDashboardCorner);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.title_name, OBJPROP_XDISTANCE, InpDashboardX + 10);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.title_name, OBJPROP_YDISTANCE, InpDashboardY + 8);
    ObjectSetString(g_dashboard.chart_id, g_dashboard.title_name, OBJPROP_TEXT, "交易时段仪表盘");
    ObjectSetString(g_dashboard.chart_id, g_dashboard.title_name, OBJPROP_FONT, InpDashboardFont);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.title_name, OBJPROP_FONTSIZE, InpDashboardFontSize + 2);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.title_name, OBJPROP_COLOR, InpDashboardTitleColor);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.title_name, OBJPROP_BACK, false);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.title_name, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.title_name, OBJPROP_SELECTED, false);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.title_name, OBJPROP_HIDDEN, true);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.title_name, OBJPROP_ZORDER, 1);
    
    return true;
}

//+------------------------------------------------------------------+
//| 创建时段信息标签                                                 |
//+------------------------------------------------------------------+
bool CreateSessionLabel()
{
    if(!ObjectCreate(g_dashboard.chart_id, g_dashboard.session_name, OBJ_LABEL, 0, 0, 0))
    {
        LogError("创建时段信息标签对象失败", __FUNCTION__);
        return false;
    }
    
    // 设置时段信息属性
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.session_name, OBJPROP_CORNER, InpDashboardCorner);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.session_name, OBJPROP_XDISTANCE, InpDashboardX + 10);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.session_name, OBJPROP_YDISTANCE, InpDashboardY + 35);
    ObjectSetString(g_dashboard.chart_id, g_dashboard.session_name, OBJPROP_TEXT, "当前时段: 加载中...");
    ObjectSetString(g_dashboard.chart_id, g_dashboard.session_name, OBJPROP_FONT, InpDashboardFont);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.session_name, OBJPROP_FONTSIZE, InpDashboardFontSize);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.session_name, OBJPROP_COLOR, InpDashboardTextColor);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.session_name, OBJPROP_BACK, false);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.session_name, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.session_name, OBJPROP_SELECTED, false);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.session_name, OBJPROP_HIDDEN, true);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.session_name, OBJPROP_ZORDER, 1);
    
    return true;
}

//+------------------------------------------------------------------+
//| 创建波幅信息标签                                                 |
//+------------------------------------------------------------------+
bool CreateRangeInfoLabel()
{
    if(!ObjectCreate(g_dashboard.chart_id, g_dashboard.range_info_name, OBJ_LABEL, 0, 0, 0))
    {
        LogError("创建波幅信息标签对象失败", __FUNCTION__);
        return false;
    }
    
    // 设置波幅信息属性
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.range_info_name, OBJPROP_CORNER, InpDashboardCorner);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.range_info_name, OBJPROP_XDISTANCE, InpDashboardX + 10);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.range_info_name, OBJPROP_YDISTANCE, InpDashboardY + 55);
    ObjectSetString(g_dashboard.chart_id, g_dashboard.range_info_name, OBJPROP_TEXT, "波幅信息: 计算中...");
    ObjectSetString(g_dashboard.chart_id, g_dashboard.range_info_name, OBJPROP_FONT, InpDashboardFont);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.range_info_name, OBJPROP_FONTSIZE, InpDashboardFontSize);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.range_info_name, OBJPROP_COLOR, InpDashboardTextColor);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.range_info_name, OBJPROP_BACK, false);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.range_info_name, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.range_info_name, OBJPROP_SELECTED, false);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.range_info_name, OBJPROP_HIDDEN, true);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.range_info_name, OBJPROP_ZORDER, 1);
    
    return true;
}

//+------------------------------------------------------------------+
//| 创建百分比标签                                                   |
//+------------------------------------------------------------------+
bool CreatePercentageLabel()
{
    if(!ObjectCreate(g_dashboard.chart_id, g_dashboard.percentage_name, OBJ_LABEL, 0, 0, 0))
    {
        LogError("创建百分比标签对象失败", __FUNCTION__);
        return false;
    }
    
    // 设置百分比属性
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.percentage_name, OBJPROP_CORNER, InpDashboardCorner);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.percentage_name, OBJPROP_XDISTANCE, InpDashboardX + 10);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.percentage_name, OBJPROP_YDISTANCE, InpDashboardY + 75);
    ObjectSetString(g_dashboard.chart_id, g_dashboard.percentage_name, OBJPROP_TEXT, "完成度: 0.0%");
    ObjectSetString(g_dashboard.chart_id, g_dashboard.percentage_name, OBJPROP_FONT, InpDashboardFont);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.percentage_name, OBJPROP_FONTSIZE, InpDashboardFontSize);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.percentage_name, OBJPROP_COLOR, InpDashboardTextColor);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.percentage_name, OBJPROP_BACK, false);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.percentage_name, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.percentage_name, OBJPROP_SELECTED, false);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.percentage_name, OBJPROP_HIDDEN, true);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.percentage_name, OBJPROP_ZORDER, 1);
    
    return true;
}

//+------------------------------------------------------------------+
//| 创建EMA信息标签                                                  |
//+------------------------------------------------------------------+
bool CreateEMAInfoLabel()
{
    if(!ObjectCreate(g_dashboard.chart_id, g_dashboard.ema_info_name, OBJ_LABEL, 0, 0, 0))
    {
        LogError("创建EMA信息标签对象失败", __FUNCTION__);
        return false;
    }
    
    // 设置EMA信息属性
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.ema_info_name, OBJPROP_CORNER, InpDashboardCorner);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.ema_info_name, OBJPROP_XDISTANCE, InpDashboardX + 10);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.ema_info_name, OBJPROP_YDISTANCE, InpDashboardY + 95);
    ObjectSetString(g_dashboard.chart_id, g_dashboard.ema_info_name, OBJPROP_TEXT, "EMA状态: 初始化中...");
    ObjectSetString(g_dashboard.chart_id, g_dashboard.ema_info_name, OBJPROP_FONT, InpDashboardFont);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.ema_info_name, OBJPROP_FONTSIZE, InpDashboardFontSize);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.ema_info_name, OBJPROP_COLOR, InpDashboardTextColor);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.ema_info_name, OBJPROP_BACK, false);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.ema_info_name, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.ema_info_name, OBJPROP_SELECTED, false);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.ema_info_name, OBJPROP_HIDDEN, true);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.ema_info_name, OBJPROP_ZORDER, 1);
    
    return true;
}

//+------------------------------------------------------------------+
//| 创建时间信息标签                                                 |
//+------------------------------------------------------------------+
bool CreateTimeInfoLabel()
{
    if(!ObjectCreate(g_dashboard.chart_id, g_dashboard.time_info_name, OBJ_LABEL, 0, 0, 0))
    {
        LogError("创建时间信息标签对象失败", __FUNCTION__);
        return false;
    }
    
    // 设置时间信息属性
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.time_info_name, OBJPROP_CORNER, InpDashboardCorner);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.time_info_name, OBJPROP_XDISTANCE, InpDashboardX + 10);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.time_info_name, OBJPROP_YDISTANCE, InpDashboardY + 115);
    ObjectSetString(g_dashboard.chart_id, g_dashboard.time_info_name, OBJPROP_TEXT, "GMT时间: " + TimeToString(TimeGMT(), TIME_MINUTES));
    ObjectSetString(g_dashboard.chart_id, g_dashboard.time_info_name, OBJPROP_FONT, InpDashboardFont);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.time_info_name, OBJPROP_FONTSIZE, InpDashboardFontSize - 1);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.time_info_name, OBJPROP_COLOR, InpDashboardTextColor);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.time_info_name, OBJPROP_BACK, false);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.time_info_name, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.time_info_name, OBJPROP_SELECTED, false);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.time_info_name, OBJPROP_HIDDEN, true);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.time_info_name, OBJPROP_ZORDER, 1);
    
    return true;
}

//+------------------------------------------------------------------+
//| 创建状态信息标签                                                 |
//+------------------------------------------------------------------+
bool CreateStatusLabel()
{
    if(!ObjectCreate(g_dashboard.chart_id, g_dashboard.status_name, OBJ_LABEL, 0, 0, 0))
    {
        LogError("创建状态信息标签对象失败", __FUNCTION__);
        return false;
    }
    
    // 设置状态信息属性
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.status_name, OBJPROP_CORNER, InpDashboardCorner);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.status_name, OBJPROP_XDISTANCE, InpDashboardX + 10);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.status_name, OBJPROP_YDISTANCE, InpDashboardY + 135);
    ObjectSetString(g_dashboard.chart_id, g_dashboard.status_name, OBJPROP_TEXT, "状态: 正常运行");
    ObjectSetString(g_dashboard.chart_id, g_dashboard.status_name, OBJPROP_FONT, InpDashboardFont);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.status_name, OBJPROP_FONTSIZE, InpDashboardFontSize - 1);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.status_name, OBJPROP_COLOR, clrLimeGreen);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.status_name, OBJPROP_BACK, false);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.status_name, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.status_name, OBJPROP_SELECTED, false);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.status_name, OBJPROP_HIDDEN, true);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.status_name, OBJPROP_ZORDER, 1);
    
    return true;
}

//+------------------------------------------------------------------+
//| 更新仪表盘内容                                                   |
//+------------------------------------------------------------------+
bool UpdateDashboardContent()
{
    PERFORMANCE_MONITOR();
    
    if(!g_dashboard.is_created)
    {
        LogDebug("仪表盘未创建，跳过内容更新", __FUNCTION__);
        return false;
    }
    
    LogDebug("开始更新仪表盘内容", __FUNCTION__);
    
    // 1. 更新时段信息
    UpdateSessionInfo();
    
    // 2. 更新波幅信息
    UpdateRangeInfo();
    
    // 3. 更新百分比信息
    UpdatePercentageInfo();
    
    // 4. 更新EMA信息
    UpdateEMAInfo();
    
    // 5. 更新时间信息
    UpdateTimeInfo();
    
    // 6. 更新状态信息
    UpdateStatusInfo();
    
    // 更新时间戳
    g_dashboard.last_update = TimeCurrent();
    
    LogDebug("仪表盘内容更新完成", __FUNCTION__);
    return true;
}

//+------------------------------------------------------------------+
//| 更新时段信息                                                     |
//+------------------------------------------------------------------+
void UpdateSessionInfo()
{
    string session_text = "当前时段: " + g_context.current_session_name;
    
    // 根据时段设置不同颜色
    color session_color = InpDashboardTextColor;
    switch(g_context.current_session)
    {
        case SESSION_ASIA:    session_color = clrYellow; break;
        case SESSION_LONDON:  session_color = clrLimeGreen; break;
        case SESSION_OVERLAP: session_color = clrOrange; break;
        case SESSION_NEWYORK: session_color = clrDeepSkyBlue; break;
        default:              session_color = clrGray; break;
    }
    
    ObjectSetString(g_dashboard.chart_id, g_dashboard.session_name, OBJPROP_TEXT, session_text);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.session_name, OBJPROP_COLOR, session_color);
}

//+------------------------------------------------------------------+
//| 更新波幅信息                                                     |
//+------------------------------------------------------------------+
void UpdateRangeInfo()
{
    string range_text = StringFormat("今日波幅: %.0f | 日均: %.0f | 剩余: %.0f", 
                                     g_context.today_range * MathPow(10, _Digits),
                                     g_context.daily_avg_range * MathPow(10, _Digits),
                                     g_context.remaining_range * MathPow(10, _Digits));
    
    ObjectSetString(g_dashboard.chart_id, g_dashboard.range_info_name, OBJPROP_TEXT, range_text);
}

//+------------------------------------------------------------------+
//| 更新百分比信息                                                   |
//+------------------------------------------------------------------+
void UpdatePercentageInfo()
{
    string percentage_text = StringFormat("完成度: %.1f%%", g_context.percentage);
    
    // 根据完成度设置颜色
    color percentage_color = InpDashboardTextColor;
    if(g_context.percentage >= 100.0)
        percentage_color = clrRed;
    else if(g_context.percentage >= 80.0)
        percentage_color = clrOrange;
    else if(g_context.percentage >= 60.0)
        percentage_color = clrYellow;
    else
        percentage_color = clrLimeGreen;
    
    ObjectSetString(g_dashboard.chart_id, g_dashboard.percentage_name, OBJPROP_TEXT, percentage_text);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.percentage_name, OBJPROP_COLOR, percentage_color);
}

//+------------------------------------------------------------------+
//| 更新EMA信息                                                      |
//+------------------------------------------------------------------+
void UpdateEMAInfo()
{
    string ema_text = GetEMAInfoSummary();
    
    // 根据EMA排列设置颜色
    color ema_color = InpDashboardTextColor;
    switch(g_context.ema_alignment)
    {
        case EMA_ALIGNMENT_BULLISH: ema_color = clrLimeGreen; break;
        case EMA_ALIGNMENT_BEARISH: ema_color = clrRed; break;
        case EMA_ALIGNMENT_MIXED:   ema_color = clrYellow; break;
        default:                    ema_color = clrGray; break;
    }
    
    ObjectSetString(g_dashboard.chart_id, g_dashboard.ema_info_name, OBJPROP_TEXT, ema_text);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.ema_info_name, OBJPROP_COLOR, ema_color);
}

//+------------------------------------------------------------------+
//| 更新时间信息                                                     |
//+------------------------------------------------------------------+
void UpdateTimeInfo()
{
    string time_text = "GMT时间: " + TimeToString(TimeGMT(), TIME_MINUTES);
    ObjectSetString(g_dashboard.chart_id, g_dashboard.time_info_name, OBJPROP_TEXT, time_text);
}

//+------------------------------------------------------------------+
//| 更新状态信息                                                     |
//+------------------------------------------------------------------+
void UpdateStatusInfo()
{
    string status_text = "状态: ";
    color status_color = clrLimeGreen;
    
    // 检查各种状态
    if(!ValidateEMADataIntegrity())
    {
        status_text += "EMA数据异常";
        status_color = clrRed;
    }
    else if(g_context.today_range <= 0)
    {
        status_text += "波幅数据异常";
        status_color = clrOrange;
    }
    else if(TimeCurrent() - g_dashboard.last_update > UPDATE_INTERVAL * 2)
    {
        status_text += "更新延迟";
        status_color = clrYellow;
    }
    else
    {
        status_text += "正常运行";
        status_color = clrLimeGreen;
    }
    
    ObjectSetString(g_dashboard.chart_id, g_dashboard.status_name, OBJPROP_TEXT, status_text);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.status_name, OBJPROP_COLOR, status_color);
}

//+------------------------------------------------------------------+
//| 强制刷新仪表盘                                                   |
//+------------------------------------------------------------------+
void ForceRefreshDashboard()
{
    LogInfo("强制刷新仪表盘显示", __FUNCTION__);
    
    if(g_dashboard.is_created)
    {
        UpdateDashboardContent();
        ChartRedraw(g_dashboard.chart_id);
    }
    else
    {
        LogWarning("仪表盘未创建，无法刷新", __FUNCTION__);
    }
}

//+------------------------------------------------------------------+
//| 清理仪表盘对象                                                   |
//+------------------------------------------------------------------+
void CleanupDashboard()
{
    LogInfo("开始清理仪表盘对象", __FUNCTION__);
    
    // 删除所有仪表盘对象
    ObjectDelete(g_dashboard.chart_id, g_dashboard.background_name);
    ObjectDelete(g_dashboard.chart_id, g_dashboard.title_name);
    ObjectDelete(g_dashboard.chart_id, g_dashboard.session_name);
    ObjectDelete(g_dashboard.chart_id, g_dashboard.range_info_name);
    ObjectDelete(g_dashboard.chart_id, g_dashboard.percentage_name);
    ObjectDelete(g_dashboard.chart_id, g_dashboard.ema_info_name);
    ObjectDelete(g_dashboard.chart_id, g_dashboard.time_info_name);
    ObjectDelete(g_dashboard.chart_id, g_dashboard.status_name);
    
    // 清理所有以前缀开头的对象（防止遗漏）
    int total_objects = ObjectsTotal(g_dashboard.chart_id);
    for(int i = total_objects - 1; i >= 0; i--)
    {
        string obj_name = ObjectName(g_dashboard.chart_id, i);
        if(StringFind(obj_name, DASHBOARD_PREFIX) == 0)
        {
            ObjectDelete(g_dashboard.chart_id, obj_name);
            LogDebug(StringFormat("删除遗留对象: %s", obj_name), __FUNCTION__);
        }
    }
    
    // 重置状态
    g_dashboard.is_created = false;
    g_dashboard.last_update = 0;
    
    // 强制重绘图表
    ChartRedraw(g_dashboard.chart_id);
    
    LogInfo("仪表盘对象清理完成", __FUNCTION__);
}

//+------------------------------------------------------------------+
//| 检查仪表盘对象完整性                                             |
//+------------------------------------------------------------------+
bool ValidateDashboardIntegrity()
{
    if(!g_dashboard.is_created) return false;
    
    // 检查所有关键对象是否存在
    bool all_exist = true;
    
    if(ObjectFind(g_dashboard.chart_id, g_dashboard.background_name) < 0)
    {
        LogWarning("背景对象丢失", __FUNCTION__);
        all_exist = false;
    }
    
    if(ObjectFind(g_dashboard.chart_id, g_dashboard.title_name) < 0)
    {
        LogWarning("标题对象丢失", __FUNCTION__);
        all_exist = false;
    }
    
    if(ObjectFind(g_dashboard.chart_id, g_dashboard.session_name) < 0)
    {
        LogWarning("时段信息对象丢失", __FUNCTION__);
        all_exist = false;
    }
    
    if(ObjectFind(g_dashboard.chart_id, g_dashboard.range_info_name) < 0)
    {
        LogWarning("波幅信息对象丢失", __FUNCTION__);
        all_exist = false;
    }
    
    if(ObjectFind(g_dashboard.chart_id, g_dashboard.percentage_name) < 0)
    {
        LogWarning("百分比对象丢失", __FUNCTION__);
        all_exist = false;
    }
    
    if(ObjectFind(g_dashboard.chart_id, g_dashboard.ema_info_name) < 0)
    {
        LogWarning("EMA信息对象丢失", __FUNCTION__);
        all_exist = false;
    }
    
    if(ObjectFind(g_dashboard.chart_id, g_dashboard.time_info_name) < 0)
    {
        LogWarning("时间信息对象丢失", __FUNCTION__);
        all_exist = false;
    }
    
    if(ObjectFind(g_dashboard.chart_id, g_dashboard.status_name) < 0)
    {
        LogWarning("状态信息对象丢失", __FUNCTION__);
        all_exist = false;
    }
    
    if(!all_exist)
    {
        LogError("仪表盘对象完整性检查失败", __FUNCTION__);
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 重建仪表盘（修复损坏的对象）                                     |
//+------------------------------------------------------------------+
bool RebuildDashboard()
{
    LogInfo("开始重建仪表盘", __FUNCTION__);
    
    // 清理现有对象
    CleanupDashboard();
    
    // 重新初始化
    if(!InitializeDashboard())
    {
        LogError("重建仪表盘失败", __FUNCTION__);
        return false;
    }
    
    LogInfo("仪表盘重建完成", __FUNCTION__);
    return true;
}

//+------------------------------------------------------------------+
//| 获取仪表盘状态信息                                               |
//+------------------------------------------------------------------+
string GetDashboardStatusInfo()
{
    string status = "=== 仪表盘状态信息 ===\n";
    
    status += StringFormat("创建状态: %s\n", g_dashboard.is_created ? "已创建" : "未创建");
    status += StringFormat("图表ID: %lld\n", g_dashboard.chart_id);
    status += StringFormat("最后更新: %s\n", 
                          g_dashboard.last_update > 0 ? TimeToString(g_dashboard.last_update, TIME_MINUTES) : "从未更新");
    
    if(g_dashboard.is_created)
    {
        status += StringFormat("对象完整性: %s\n", ValidateDashboardIntegrity() ? "完整" : "损坏");
        
        // 检查各个对象的存在状态
        status += StringFormat("背景对象: %s\n", ObjectFind(g_dashboard.chart_id, g_dashboard.background_name) >= 0 ? "存在" : "丢失");
        status += StringFormat("标题对象: %s\n", ObjectFind(g_dashboard.chart_id, g_dashboard.title_name) >= 0 ? "存在" : "丢失");
        status += StringFormat("时段对象: %s\n", ObjectFind(g_dashboard.chart_id, g_dashboard.session_name) >= 0 ? "存在" : "丢失");
        status += StringFormat("波幅对象: %s\n", ObjectFind(g_dashboard.chart_id, g_dashboard.range_info_name) >= 0 ? "存在" : "丢失");
        status += StringFormat("百分比对象: %s\n", ObjectFind(g_dashboard.chart_id, g_dashboard.percentage_name) >= 0 ? "存在" : "丢失");
        status += StringFormat("EMA对象: %s\n", ObjectFind(g_dashboard.chart_id, g_dashboard.ema_info_name) >= 0 ? "存在" : "丢失");
        status += StringFormat("时间对象: %s\n", ObjectFind(g_dashboard.chart_id, g_dashboard.time_info_name) >= 0 ? "存在" : "丢失");
        status += StringFormat("状态对象: %s\n", ObjectFind(g_dashboard.chart_id, g_dashboard.status_name) >= 0 ? "存在" : "丢失");
    }
    
    return status;
}

//+------------------------------------------------------------------+
//| 设置仪表盘可见性                                                 |
//+------------------------------------------------------------------+
void SetDashboardVisibility(bool visible)
{
    if(!g_dashboard.is_created)
    {
        LogWarning("仪表盘未创建，无法设置可见性", __FUNCTION__);
        return;
    }
    
    LogInfo(StringFormat("设置仪表盘可见性: %s", visible ? "显示" : "隐藏"), __FUNCTION__);
    
    // 设置所有对象的可见性
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.background_name, OBJPROP_TIMEFRAMES, visible ? OBJ_ALL_PERIODS : OBJ_NO_PERIODS);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.title_name, OBJPROP_TIMEFRAMES, visible ? OBJ_ALL_PERIODS : OBJ_NO_PERIODS);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.session_name, OBJPROP_TIMEFRAMES, visible ? OBJ_ALL_PERIODS : OBJ_NO_PERIODS);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.range_info_name, OBJPROP_TIMEFRAMES, visible ? OBJ_ALL_PERIODS : OBJ_NO_PERIODS);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.percentage_name, OBJPROP_TIMEFRAMES, visible ? OBJ_ALL_PERIODS : OBJ_NO_PERIODS);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.ema_info_name, OBJPROP_TIMEFRAMES, visible ? OBJ_ALL_PERIODS : OBJ_NO_PERIODS);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.time_info_name, OBJPROP_TIMEFRAMES, visible ? OBJ_ALL_PERIODS : OBJ_NO_PERIODS);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.status_name, OBJPROP_TIMEFRAMES, visible ? OBJ_ALL_PERIODS : OBJ_NO_PERIODS);
    
    ChartRedraw(g_dashboard.chart_id);
}

//+------------------------------------------------------------------+
//| 更新仪表盘位置                                                   |
//+------------------------------------------------------------------+
void UpdateDashboardPosition(int x, int y)
{
    if(!g_dashboard.is_created)
    {
        LogWarning("仪表盘未创建，无法更新位置", __FUNCTION__);
        return;
    }
    
    LogInfo(StringFormat("更新仪表盘位置: (%d, %d)", x, y), __FUNCTION__);
    
    // 更新背景位置
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.background_name, OBJPROP_XDISTANCE, x);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.background_name, OBJPROP_YDISTANCE, y);
    
    // 更新所有文本对象位置
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.title_name, OBJPROP_XDISTANCE, x + 10);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.title_name, OBJPROP_YDISTANCE, y + 8);
    
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.session_name, OBJPROP_XDISTANCE, x + 10);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.session_name, OBJPROP_YDISTANCE, y + 35);
    
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.range_info_name, OBJPROP_XDISTANCE, x + 10);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.range_info_name, OBJPROP_YDISTANCE, y + 55);
    
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.percentage_name, OBJPROP_XDISTANCE, x + 10);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.percentage_name, OBJPROP_YDISTANCE, y + 75);
    
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.ema_info_name, OBJPROP_XDISTANCE, x + 10);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.ema_info_name, OBJPROP_YDISTANCE, y + 95);
    
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.time_info_name, OBJPROP_XDISTANCE, x + 10);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.time_info_name, OBJPROP_YDISTANCE, y + 115);
    
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.status_name, OBJPROP_XDISTANCE, x + 10);
    ObjectSetInteger(g_dashboard.chart_id, g_dashboard.status_name, OBJPROP_YDISTANCE, y + 135);
    
    ChartRedraw(g_dashboard.chart_id);
}

//+------------------------------------------------------------------+
//| 检查是否需要更新仪表盘                                           |
//+------------------------------------------------------------------+
bool ShouldUpdateDashboard()
{
    if(!g_dashboard.is_created) return false;
    
    // 检查更新间隔
    if(TimeCurrent() - g_dashboard.last_update < UPDATE_INTERVAL) return false;
    
    // 检查价格是否有变化
    if(!g_context.price_cache.has_change) return false;
    
    // 检查对象完整性
    if(!ValidateDashboardIntegrity()) 
    {
        LogWarning("仪表盘对象损坏，需要重建", __FUNCTION__);
        return true; // 需要重建
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 智能更新仪表盘（仅在必要时更新）                                 |
//+------------------------------------------------------------------+
void SmartUpdateDashboard()
{
    PERFORMANCE_MONITOR();
    
    if(!ShouldUpdateDashboard()) return;
    
    // 如果对象完整性检查失败，尝试重建
    if(!ValidateDashboardIntegrity())
    {
        LogWarning("检测到仪表盘对象损坏，尝试重建", __FUNCTION__);
        if(!RebuildDashboard())
        {
            LogError("仪表盘重建失败", __FUNCTION__);
            return;
        }
    }
    
    // 更新内容
    UpdateDashboardContent();
    
    LogDebug("智能更新仪表盘完成", __FUNCTION__);
}

//+------------------------------------------------------------------+
//| 获取仪表盘性能统计                                               |
//+------------------------------------------------------------------+
string GetDashboardPerformanceStats()
{
    string stats = "=== 仪表盘性能统计 ===\n";
    
    stats += StringFormat("创建时间: %s\n", 
                         g_dashboard.is_created ? "已创建" : "未创建");
    
    if(g_dashboard.last_update > 0)
    {
        int seconds_since_update = (int)(TimeCurrent() - g_dashboard.last_update);
        stats += StringFormat("距离上次更新: %d秒\n", seconds_since_update);
        stats += StringFormat("更新频率: %s\n", 
                             seconds_since_update < UPDATE_INTERVAL ? "正常" : "延迟");
    }
    else
    {
        stats += "更新状态: 从未更新\n";
    }
    
    stats += StringFormat("对象数量: %d个\n", 8); // 固定8个对象
    stats += StringFormat("内存占用: 约%d字节\n", 8 * 200); // 估算值
    
    return stats;
}

//+------------------------------------------------------------------+
//| 导出仪表盘配置                                                   |
//+------------------------------------------------------------------+
string ExportDashboardConfig()
{
    string config = "=== 仪表盘配置导出 ===\n";
    
    config += StringFormat("位置: (%d, %d)\n", InpDashboardX, InpDashboardY);
    config += StringFormat("角落: %d\n", InpDashboardCorner);
    config += StringFormat("尺寸: %dx%d\n", DASHBOARD_WIDTH, DASHBOARD_HEIGHT);
    config += StringFormat("背景色: %d\n", InpDashboardBgColor);
    config += StringFormat("边框色: %d\n", InpDashboardBorderColor);
    config += StringFormat("标题色: %d\n", InpDashboardTitleColor);
    config += StringFormat("文本色: %d\n", InpDashboardTextColor);
    config += StringFormat("字体: %s\n", InpDashboardFont);
    config += StringFormat("字体大小: %d\n", InpDashboardFontSize);
    config += StringFormat("显示状态: %s\n", InpShowDashboard ? "显示" : "隐藏");
    
    return config;
}

//+------------------------------------------------------------------+
//| 仪表盘模块自检                                                   |
//+------------------------------------------------------------------+
bool DashboardSelfCheck()
{
    LogInfo("开始仪表盘模块自检", __FUNCTION__);
    
    bool all_ok = true;
    
    // 1. 检查配置参数
    if(InpDashboardX < 0 || InpDashboardX > 2000)
    {
        LogError(StringFormat("仪表盘X坐标超出范围: %d", InpDashboardX), __FUNCTION__);
        all_ok = false;
    }
    
    if(InpDashboardY < 0 || InpDashboardY > 2000)
    {
        LogError(StringFormat("仪表盘Y坐标超出范围: %d", InpDashboardY), __FUNCTION__);
        all_ok = false;
    }
    
    if(InpDashboardCorner < 0 || InpDashboardCorner > 3)
    {
        LogError(StringFormat("仪表盘角落参数无效: %d", InpDashboardCorner), __FUNCTION__);
        all_ok = false;
    }
    
    if(InpDashboardFontSize < 6 || InpDashboardFontSize > 20)
    {
        LogError(StringFormat("仪表盘字体大小超出范围: %d", InpDashboardFontSize), __FUNCTION__);
        all_ok = false;
    }
    
    // 2. 检查图表ID有效性
    if(ChartID() <= 0)
    {
        LogError("图表ID无效", __FUNCTION__);
        all_ok = false;
    }
    
    // 3. 检查创建状态
    if(g_dashboard.is_created)
    {
        if(!ValidateDashboardIntegrity())
        {
            LogWarning("仪表盘对象完整性检查失败", __FUNCTION__);
            all_ok = false;
        }
    }
    
    if(all_ok)
    {
        LogInfo("仪表盘模块自检通过", __FUNCTION__);
    }
    else
    {
        LogError("仪表盘模块自检失败", __FUNCTION__);
    }
    
    return all_ok;
}

//+------------------------------------------------------------------+
//| 初始化仪表盘模块                                                 |
//+------------------------------------------------------------------+
bool InitializeDashboardModule()
{
    // 仪表盘初始化逻辑
    return true;
}

//+------------------------------------------------------------------+
//| 清理仪表盘模块                                                   |
//+------------------------------------------------------------------+
void CleanupDashboardModule()
{
    // 清理仪表盘资源
}
